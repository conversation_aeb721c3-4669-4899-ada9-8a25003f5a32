services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-db
    environment:
      POSTGRES_USER: spheroseg
      POSTGRES_PASSWORD: spheroseg_dev
      POSTGRES_DB: spheroseg
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Redis cache service
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    networks:
      - spheroseg-network
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MailHog email testing service
  mailhog:
    image: mailhog/mailhog
    container_name: spheroseg-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Frontend service (will connect to backend when it's available)
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: spheroseg-frontend
    environment:
      - VITE_API_URL=http://localhost:3001
      - VITE_API_BASE_URL=http://localhost:3001/api
      - VITE_ML_SERVICE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:3001
      - NODE_ENV=development
    ports:
      - "3000:5173"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - ./index.html:/app/index.html
      - ./vite.config.ts:/app/vite.config.ts
      - ./tailwind.config.ts:/app/tailwind.config.ts
      - ./postcss.config.js:/app/postcss.config.js
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.app.json:/app/tsconfig.app.json
    networks:
      - spheroseg-network
    restart: unless-stopped

networks:
  spheroseg-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local