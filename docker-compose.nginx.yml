version: '3.8'

services:
  nginx-main:
    image: nginx:alpine
    container_name: nginx-main
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./certbot/www:/var/www/certbot:ro
    depends_on:
      - nginx-green
    networks:
      - green-network
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  green-network:
    external: true
    name: cell-segmentation-hub_green-network