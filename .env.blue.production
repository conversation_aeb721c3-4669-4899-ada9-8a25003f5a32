# Blue Production Environment Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Database
DATABASE_URL=*************************************************************/spheroseg_blue
DB_PASSWORD=spheroseg_blue_2024

# JWT Secrets
JWT_ACCESS_SECRET=b75d09c9e67acfe64cf2ff2ebe704648b2b6deba44b1eea6bed51a66b325fd41
JWT_REFRESH_SECRET=b1e6ae77c4da116fe524c057879c0779a7fe5f3cc26a59bbc1ab3ef482bc0a3d
BLUE_JWT_ACCESS_SECRET=b75d09c9e67acfe64cf2ff2ebe704648b2b6deba44b1eea6bed51a66b325fd41  
BLUE_JWT_REFRESH_SECRET=b1e6ae77c4da116fe524c057879c0779a7fe5f3cc26a59bbc1ab3ef482bc0a3d

# Email Service - WORKING CONFIGURATION WITH AUTH
EMAIL_SERVICE=smtp
SMTP_HOST=mail.utia.cas.cz
SMTP_PORT=25
SMTP_SECURE=false
SMTP_REQUIRE_TLS=true
SMTP_AUTH=true
SMTP_USER=<EMAIL>
SMTP_PASS=M1i2c3h4a5l6
FROM_EMAIL=<EMAIL>
FROM_NAME=Cell Segmentation Platform

# TLS/SSL Configuration
SMTP_IGNORE_TLS=false
EMAIL_ALLOW_INSECURE=false

# Timeout Settings - Extended for UTIA SMTP server extreme delays (up to 10 min)
EMAIL_TIMEOUT=300000
SMTP_CONNECTION_TIMEOUT_MS=15000
SMTP_GREETING_TIMEOUT_MS=15000  
SMTP_SOCKET_TIMEOUT_MS=600000

# Email Retry Configuration - Reduced for faster failure
EMAIL_MAX_RETRIES=2
EMAIL_RETRY_INITIAL_DELAY=1000
EMAIL_RETRY_MAX_DELAY=10000
EMAIL_RETRY_BACKOFF_FACTOR=2

# Global timeout protection - 10 minutes for extreme UTIA delays
EMAIL_GLOBAL_TIMEOUT=600000

# Email Features
SKIP_EMAIL_SEND=false
REQUIRE_EMAIL_VERIFICATION=false

# Debug (disable in production)
SMTP_DEBUG=false
EMAIL_DEBUG=false

# Redis
REDIS_URL=redis://redis-blue:6379

# ML Service
SEGMENTATION_SERVICE_URL=http://blue-ml:8000

# File Storage
STORAGE_TYPE=local
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# CORS and Origins
ALLOWED_ORIGINS=http://localhost:4000,http://localhost:4080,https://spherosegapp.utia.cas.cz
WS_ALLOWED_ORIGINS=http://localhost:4000,http://localhost:4080,https://spherosegapp.utia.cas.cz,ws://localhost:4001,wss://spherosegapp.utia.cas.cz
CORS_ORIGIN=http://localhost:4000,http://localhost:4080,https://spherosegapp.utia.cas.cz

# Frontend URLs
FRONTEND_URL=https://spherosegapp.utia.cas.cz
API_BASE_URL=https://spherosegapp.utia.cas.cz/api
BACKEND_URL=https://spherosegapp.utia.cas.cz/api
PUBLIC_URL=https://spherosegapp.utia.cas.cz

# Vite URLs for frontend build
VITE_API_BASE_URL=/api
VITE_ML_SERVICE_URL=/api/ml
VITE_WS_URL=wss://spherosegapp.utia.cas.cz

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=2000

# Monitoring
OTEL_SAMPLING_RATE=0.1