{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "type-check:strict": "tsc --noEmit -p tsconfig.strict.json", "type-check:backend": "cd backend && tsc --noEmit --maxNodeModuleJsDepth 0", "type-check:all": "npm run type-check && npm run type-check:backend", "type-coverage": "npx type-coverage --detail --strict --ignore-files '**/*.test.*' '**/*.spec.*'", "type-fix": "npx ts-migrate migrate src/", "strict-candidates": "node scripts/find-strict-candidates.js", "migration-report": "node scripts/generate-migration-report.js", "validate:types": "npm run type-check:all && npm run type-coverage", "precommit": "lint-staged", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "i18n:check": "node scripts/check-i18n.cjs", "i18n:lint": "eslint . --config .eslintrc-i18n.js", "i18n:validate": "npm run i18n:check && npm run i18n:lint", "lint:types": "eslint . --ext .ts,.tsx --config .eslintrc-typescript.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down --volumes --remove-orphans && docker system prune -f", "docker:dev": "docker-compose up -d && docker-compose logs -f", "prepare": "husky", "security:check": "node scripts/security-check.js", "security:audit": "npm audit --production", "security:full": "npm run security:check && npm run security:audit", "test:critical": "vitest run --run src/lib/api.test.ts src/contexts/__tests__/AuthContext.test.tsx", "test:performance": "vitest run --run tests/performance/*.spec.ts --reporter=verbose", "test:parallel": "vitest run --run --threads --minThreads=2 --maxThreads=4", "test:watch:focused": "vitest --watch --testNamePattern"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "form-data": "^4.0.4", "framer-motion": "^12.5.0", "i18next": "^25.4.2", "input-otp": "^1.2.4", "jszip": "^3.10.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.5.0", "react-hook-form": "^7.53.0", "react-i18next": "^15.7.3", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sharp": "^0.34.3", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tiff.js": "^1.0.0", "utif2": "^4.1.0", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.33.0", "@playwright/test": "^1.54.2", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/file-saver": "^2.0.7", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "axe-playwright": "^2.1.0", "axios-mock-adapter": "^2.1.0", "canvas": "^2.11.2", "depcheck": "^1.4.7", "eslint": "^9.33.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "glob": "^11.0.0", "globals": "^15.15.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^16.1.5", "madge": "^8.0.0", "msw": "^2.6.4", "postcss": "^8.4.47", "prettier": "^3.6.2", "shadcn": "^3.2.1", "stylelint": "^16.23.1", "stylelint-config-standard": "^39.0.0", "tailwindcss": "^3.4.11", "type-coverage": "^2.29.1", "typescript": "^5.5.3", "typescript-eslint": "^8.40.0", "vite": "^6.3.5", "vitest": "^3.2.4"}}