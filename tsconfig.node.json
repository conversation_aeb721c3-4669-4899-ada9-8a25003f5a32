{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "composite": true,
    "declaration": true,
    // Build Configuration for Node.js tools (Vite, etc.)
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    // Bundler Configuration
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    // Full Strict Mode for Build Tools (Low risk, high benefit)
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "noPropertyAccessFromIndexSignature": true,

    // Code Quality
    "noUnusedLocals": false, // TODO: Enable after cleanup
    "noUnusedParameters": false, // TODO: Enable after cleanup
    "noFallthroughCasesInSwitch": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,

    // Additional Safety
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true
  },
  "include": [
    "vite.config.ts",
    "vitest.config.ts",
    "playwright.config.ts",
    "scripts/**/*.ts",
    "*.config.ts",
    "*.config.js"
  ]
}
