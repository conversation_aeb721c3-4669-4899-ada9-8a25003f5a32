# Main Router Configuration for Blue-Green Deployment
# This configuration routes traffic to either blue or green environment
# based on various routing strategies

# Include the active environment (blue or green)
include /etc/nginx/conf.d/active-environment.conf;

# Upstream definitions for both environments
upstream blue_backend {
    server blue-backend:3001;
    keepalive 32;
}

upstream green_backend {
    server green-backend:3001;
    keepalive 32;
}

upstream blue_ml {
    server blue-ml:8000;
    keepalive 16;
}

upstream green_ml {
    server green-ml:8000;
    keepalive 16;
}

upstream blue_frontend {
    server blue-frontend:80;
    keepalive 16;
}

upstream green_frontend {
    server green-frontend:80;
    keepalive 16;
}

# Map to determine which environment to use based on various criteria
map $cookie_deployment $backend_pool {
    "green"     green_backend;
    default     blue_backend;
}

map $cookie_deployment $ml_pool {
    "green"     green_ml;
    default     blue_ml;
}

map $cookie_deployment $frontend_pool {
    "green"     green_frontend;
    default     blue_frontend;
}

# Alternative: Route based on header
map $http_x_deployment $header_backend {
    "green"     green_backend;
    "blue"      blue_backend;
    default     $backend_pool;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=30r/s;
limit_req_zone $binary_remote_addr zone=segmentation:10m rate=100r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=5r/s;
limit_conn_zone $binary_remote_addr zone=addr:10m;

# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# HTTP server - redirect to HTTPS
server {
    listen 80;
    listen 443 ssl http2;
    server_name spherosegapp.utia.cas.cz;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;
    include /etc/nginx/snippets/ssl-params.conf;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;
    
    # Add routing information to response headers
    add_header X-Routed-To $backend_pool always;
    add_header X-Deployment-Cookie $cookie_deployment always;
    
    # Client body size for uploads
    client_max_body_size 100M;
    client_body_buffer_size 1M;
    
    # Health check endpoints for both environments
    location /health/blue {
        proxy_pass http://blue_backend/health;
        proxy_set_header X-Environment "blue";
        access_log off;
    }
    
    location /health/green {
        proxy_pass http://green_backend/health;
        proxy_set_header X-Environment "green";
        access_log off;
    }
    
    # Combined health check
    location /health {
        default_type text/plain;
        return 200 "Router healthy\nActive: $backend_pool\n";
        access_log off;
    }
    
    # Admin endpoint to switch environments (protected)
    location /admin/switch {
        # Restrict to internal IPs only
        allow 127.0.0.1;
        allow **********/12;
        deny all;
        
        # Set cookie based on query parameter
        if ($arg_env = "green") {
            add_header Set-Cookie "deployment=green; Path=/; HttpOnly; Secure; SameSite=Strict" always;
            return 200 "Switched to green environment\n";
        }
        
        if ($arg_env = "blue") {
            add_header Set-Cookie "deployment=blue; Path=/; HttpOnly; Secure; SameSite=Strict" always;
            return 200 "Switched to blue environment\n";
        }
        
        return 400 "Invalid environment. Use ?env=blue or ?env=green\n";
    }
    
    # Root location - serve frontend
    location / {
        proxy_pass http://$frontend_pool;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Deployment $cookie_deployment;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://$frontend_pool;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Segmentation results endpoints - higher limits for bulk requests
    location ~ ^/api/segmentation/images/[^/]+/results$ {
        limit_req zone=segmentation burst=100 nodelay;
        limit_conn addr 15;
        
        proxy_pass http://$backend_pool;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Deployment $cookie_deployment;
        
        # Extended timeout for bulk result fetching
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
    }
    
    # API endpoints
    location /api {
        limit_req zone=api burst=80 nodelay;
        limit_conn addr 10;
        
        proxy_pass http://$backend_pool;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Deployment $cookie_deployment;
        
        # Extended timeout for long operations
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
    }
    
    # ML Service endpoints
    location /api/ml {
        limit_req zone=api burst=20 nodelay;
        
        # Remove /api prefix when proxying to ML service
        rewrite ^/api/ml(.*)$ /api/v1$1 break;
        
        proxy_pass http://$ml_pool;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Deployment $cookie_deployment;
        
        # ML operations can take longer
        proxy_read_timeout 600s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 600s;
    }
    
    # WebSocket support
    location /socket.io/ {
        proxy_pass http://$backend_pool;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Deployment $cookie_deployment;
        
        # WebSocket specific timeouts
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_connect_timeout 75s;
        
        # Disable buffering for WebSocket
        proxy_buffering off;
    }
    
    # Canary deployment endpoint (route percentage of traffic to green)
    location /api/canary {
        # Use split_clients to route traffic
        split_clients "${remote_addr}${request_uri}${http_user_agent}" $canary_backend {
            10%     green_backend;  # 10% to green
            *       blue_backend;   # 90% to blue
        }
        
        proxy_pass http://$canary_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Canary-Route $canary_backend;
    }
    
    # Metrics aggregation (combine metrics from both environments)
    location /metrics/all {
        allow 127.0.0.1;
        allow **********/12;
        deny all;
        
        default_type text/plain;
        
        # This would need a custom solution to aggregate metrics
        # For now, return basic info
        return 200 "# Blue-Green Metrics\n# Active: $backend_pool\n# Blue: http://blue_backend/metrics\n# Green: http://green_backend/metrics\n";
    }
}

# vim: set ft=nginx: