user nginx;
worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    upstream backend {
        server blue-backend:3001;
    }

    upstream frontend {
        server blue-frontend:80;
    }

    upstream ml {
        server blue-ml:8000;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name spherosegapp.utia.cas.cz;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name spherosegapp.utia.cas.cz;

        # SSL Configuration
        ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;
        
        # Modern SSL configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Support for 10,000 images (100 files per chunk * 1.5MB avg = 150MB + overhead)
        client_max_body_size 500M;
        client_body_buffer_size 256k;
        client_body_timeout 600s;
        
        # Optimize for large uploads
        proxy_request_buffering off;
        proxy_buffering off;

        # Backend API - MUST BE BEFORE CATCH-ALL
        location /api/ {
            proxy_pass http://backend/api/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            # Extended timeouts for email operations and file uploads
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_request_buffering off;
            proxy_buffering off;
        }

        # WebSocket - MUST BE BEFORE CATCH-ALL
        location /socket.io/ {
            proxy_pass http://backend/socket.io/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            # CORS headers for WebSocket
            add_header Access-Control-Allow-Origin "https://spherosegapp.utia.cas.cz" always;
            add_header Access-Control-Allow-Credentials "true" always;
            
            proxy_connect_timeout 7d;
            proxy_send_timeout 7d;
            proxy_read_timeout 7d;
        }

        # Uploads directory - USE PRIORITY PREFIX TO OVERRIDE REGEX
        location ^~ /uploads/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            # Cache images for better performance
            add_header Cache-Control "public, max-age=86400";
        }
        
        # Cache control for HTML files (MUST be before catch-all)
        location ~* \.(html|htm)$ {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            # Prevent caching of HTML files
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Pragma "no-cache" always;
            add_header Expires "0" always;
        }
        
        # Cache static assets with hashes - EXCLUDE /uploads/ path
        location ~* ^(?!/uploads/).*\.(js|css|map|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            expires 1y;
            add_header Cache-Control "public, immutable" always;
        }
        
        # Frontend - CATCH-ALL MUST BE LAST
        location / {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
        }

        # ML Service
        location /ml/ {
            proxy_pass http://ml/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_read_timeout 300s;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}