# Nginx Template Configuration for Blue-Green Deployment
# This template uses environment variables for dynamic configuration
# Use envsubst to generate the actual configuration file

# Upstream definitions using environment variables
upstream backend {
    server blue-backend:3001;
    keepalive 32;
}

upstream ml_service {
    server blue-ml:8000;
    keepalive 16;
}

upstream frontend {
    server blue-frontend:80;
    keepalive 16;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=30r/s;
limit_req_zone $binary_remote_addr zone=segmentation:10m rate=100r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=5r/s;
limit_conn_zone $binary_remote_addr zone=addr:10m;

# Map for WebSocket upgrade
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# HTTP server - redirect to HTTPS
server {
    listen ${NGINX_HTTP_PORT:-80};
    server_name spherosegapp.utia.cas.cz;
    
    # Health check endpoint (available on HTTP)
    location /health {
        access_log off;
        add_header Content-Type text/plain;
        add_header X-Environment "production-blue" always;
        return 200 "blue-production-healthy\n";
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server - main configuration
server {
    listen ${NGINX_HTTPS_PORT:-443} ssl http2;
    server_name spherosegapp.utia.cas.cz;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;
    include /etc/nginx/snippets/ssl-params.conf;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Environment "production-blue" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Client body size for uploads
    client_max_body_size 100M;
    client_body_buffer_size 1M;
    
    # Timeouts
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    send_timeout 60s;
    
    # Root location - serve frontend
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "production-blue";
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Environment "production-blue" always;
        }
    }
    
    # Segmentation results endpoints - higher limits for bulk requests
    location ~ ^/api/segmentation/images/[^/]+/results$ {
        limit_req zone=segmentation burst=100 nodelay;
        limit_conn addr 15;
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "production-blue";
        
        # Extended timeout for bulk result fetching
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
    }
    
    # API endpoints
    location /api {
        limit_req zone=api burst=80 nodelay;
        limit_conn addr 10;
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "production-blue";
        
        # Extended timeout for long operations
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
    }
    
    # ML Service endpoints
    location /api/ml {
        limit_req zone=api burst=20 nodelay;
        
        # Remove /api prefix when proxying to ML service
        rewrite ^/api/ml(.*)$ /api/v1$1 break;
        
        proxy_pass http://ml_service;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "production-blue";
        
        # ML operations can take longer
        proxy_read_timeout 600s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 600s;
        
        # Increase buffer sizes for ML responses
        proxy_buffer_size 8k;
        proxy_buffers 16 8k;
        proxy_busy_buffers_size 32k;
    }
    
    # WebSocket support
    location /socket.io/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "production-blue";
        
        # WebSocket specific timeouts
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_connect_timeout 75s;
        
        # Disable buffering for WebSocket
        proxy_buffering off;
    }
    
    # Upload endpoints with special handling
    location /api/images/upload {
        limit_req zone=upload burst=10 nodelay;
        client_max_body_size 100M;
        client_body_timeout 300s;
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "production-blue";
        
        # Upload specific timeouts
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
        
        # Disable request buffering for uploads
        proxy_request_buffering off;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        add_header Content-Type text/plain;
        add_header X-Environment "production-blue" always;
        return 200 "blue-production-healthy\n";
    }
    
    # Metrics endpoint (internal only)
    location /metrics {
        allow 127.0.0.1;
        allow **********/12;  # Docker networks
        deny all;
        
        proxy_pass http://backend/metrics;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Environment "production-blue";
    }
    
    # Static file serving for uploads
    location /uploads {
        alias /app/uploads/blue;
        expires 30d;
        add_header Cache-Control "public";
        add_header X-Environment "production-blue" always;
        
        # Security for uploaded files
        add_header X-Content-Type-Options "nosniff" always;
        location ~ \.(php|jsp|asp|aspx|cgi|pl|py|rb|sh)$ {
            deny all;
        }
    }
    
    # Robots.txt
    location = /robots.txt {
        add_header Content-Type text/plain;
        add_header X-Environment "production-blue" always;
        return 200 "User-agent: *\nDisallow: /api/\nDisallow: /uploads/\nDisallow: /metrics/\n";
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /usr/share/nginx/html;
        internal;
    }
    
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
}

# vim: set ft=nginx: