# Cell Segmentation Hub - Production Deployment Configuration
# ==============================================================
# This file centralizes all deployment parameters to prevent common errors
# and ensure consistent, safe deployments across environments.

# Environment Configuration
BLUE_PORTS_START=4000
GREEN_PORTS_START=5000
LOCAL_PORTS_START=3000

# Port Mappings
BLUE_FRONTEND_PORT=4000
BLUE_BACKEND_PORT=4001
BLUE_ML_PORT=4008
BLUE_MAILHOG_WEB=8026
BLUE_MAILHOG_SMTP=1026
BLUE_NGINX_HTTP=4080
BLUE_NGINX_HTTPS=4443

GREEN_FRONTEND_PORT=5000
GREEN_BACKEND_PORT=5001
GREEN_ML_PORT=5008
GREEN_MAILHOG_WEB=8025
GREEN_MAILHOG_SMTP=1025
GREEN_NGINX_HTTP=5080
GREEN_NGINX_HTTPS=5443

# Database Configuration
DB_USER=spheroseg
BLUE_DB_NAME=spheroseg_blue
GREEN_DB_NAME=spheroseg_green
DB_PASSWORD_ENV_VAR=DB_PASSWORD

# Upload Directory Structure
UPLOAD_BASE_DIR="./backend/uploads"
BLUE_UPLOAD_DIR="${UPLOAD_BASE_DIR}/blue"
GREEN_UPLOAD_DIR="${UPLOAD_BASE_DIR}/green"

# Required upload subdirectories
UPLOAD_SUBDIRS="images thumbnails temp"

# File Permissions (UID 1001 for Docker containers)
DOCKER_UID=1001
DOCKER_GID=1001

# Docker Compose Files
BLUE_COMPOSE_FILE="docker-compose.blue.yml"
GREEN_COMPOSE_FILE="docker-compose.green.yml"
NGINX_COMPOSE_FILE="docker-compose.nginx.yml"

# Nginx Configuration Files
NGINX_PROD_CONFIG="./docker/nginx/nginx.prod.conf"
NGINX_BLUE_CONFIG="./docker/nginx/nginx.blue.conf"
NGINX_GREEN_CONFIG="./docker/nginx/nginx.green.conf"

# SSL Certificate Paths
SSL_CERT_DIR="/etc/letsencrypt/live/spherosegapp.utia.cas.cz"
SSL_CERT_FILE="fullchain.pem"
SSL_KEY_FILE="privkey.pem"

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_RETRIES=10
HEALTH_CHECK_INTERVAL=5

# Deployment Timeouts (in seconds)
SERVICE_START_TIMEOUT=180
DB_MIGRATION_TIMEOUT=300
BACKUP_TIMEOUT=600
ROLLBACK_TIMEOUT=60

# Backup Configuration
BACKUP_DIR="./backups"
BACKUP_RETENTION_DAYS=30
MAX_BACKUPS_KEEP=10

# Production URLs
PRODUCTION_DOMAIN="spherosegapp.utia.cas.cz"
PRODUCTION_URL="https://${PRODUCTION_DOMAIN}"

# Email Configuration
SMTP_HOST="mail.utia.cas.cz"
SMTP_PORT=25
FROM_EMAIL="<EMAIL>"

# Critical Service Names (must match docker-compose.yml exactly)
BLUE_SERVICES="blue-frontend blue-backend blue-ml postgres-blue redis-blue mailhog-blue"
GREEN_SERVICES="green-frontend green-backend green-ml postgres-green redis-green"

# Docker Network Names
BLUE_NETWORK="spheroseg-blue"
GREEN_NETWORK="spheroseg-green"

# Volume Names
BLUE_POSTGRES_VOLUME="spheroseg_postgres_blue"
GREEN_POSTGRES_VOLUME="green-postgres-data"
BLUE_REDIS_VOLUME="spheroseg_redis_blue"
GREEN_REDIS_VOLUME="green-redis-data"

# Environment Variable Files
BLUE_ENV_FILE=".env.blue"
GREEN_ENV_FILE=".env.green.prod"

# Required Environment Variables (must be present in .env files)
REQUIRED_ENV_VARS="DB_PASSWORD JWT_ACCESS_SECRET JWT_REFRESH_SECRET"

# ML Model Paths
ML_WEIGHTS_DIR="./backend/segmentation/weights"
REQUIRED_ML_FILES="hrnet_w32-36af842e.pth model_final.pth"

# Pre-deployment Validation Checks
VALIDATION_CHECKS=(
    "check_docker_running"
    "check_compose_files"
    "check_env_files"
    "check_ssl_certificates"
    "check_upload_directories"
    "check_ml_weights"
    "check_database_connectivity"
    "check_nginx_config"
    "check_disk_space"
    "check_memory_usage"
)

# Post-deployment Verification Tests
VERIFICATION_TESTS=(
    "test_frontend_health"
    "test_backend_health"
    "test_ml_service_health"
    "test_database_connectivity"
    "test_redis_connectivity"
    "test_file_uploads"
    "test_user_registration"
    "test_ml_inference"
    "test_websocket_connection"
    "test_nginx_routing"
)

# Safety Checks
MIN_DISK_SPACE_GB=10
MIN_MEMORY_MB=4096
MAX_CPU_USAGE_PERCENT=80

# Logging Configuration
LOG_DIR="./logs/deployment"
LOG_RETENTION_DAYS=90

# Emergency Contacts (for alerts)
ADMIN_EMAIL="<EMAIL>"
SLACK_WEBHOOK_URL=""

# Feature Flags
ENABLE_GPU_SUPPORT=true
ENABLE_EMAIL_SERVICE=false
ENABLE_SSL=true
ENABLE_MONITORING=true
SKIP_BACKUP_ON_ROLLBACK=false

# Development/Testing Overrides
# Set these to "true" for testing deployments
DRY_RUN=false
SKIP_HEALTH_CHECKS=false
SKIP_BACKUPS=false
VERBOSE_LOGGING=true

# Nginx Upstream Templates
# These will be used to update nginx.prod.conf dynamically
NGINX_BACKEND_UPSTREAM_BLUE="server blue-backend:3001;"
NGINX_BACKEND_UPSTREAM_GREEN="server green-backend:3001;"
NGINX_ML_UPSTREAM_BLUE="server blue-ml:8000;"
NGINX_ML_UPSTREAM_GREEN="server green-ml:8000;"
NGINX_FRONTEND_UPSTREAM_BLUE="server blue-frontend:80;"
NGINX_FRONTEND_UPSTREAM_GREEN="server green-frontend:80;"

# Database Migration Settings
AUTO_MIGRATE=true
BACKUP_BEFORE_MIGRATE=true
ROLLBACK_ON_MIGRATION_FAILURE=true

# Container Resource Limits
BACKEND_MEMORY_LIMIT="2G"
FRONTEND_MEMORY_LIMIT="1G"
ML_MEMORY_LIMIT="8G"
DB_MEMORY_LIMIT="2G"
REDIS_MEMORY_LIMIT="512M"

# Security Settings
ENABLE_RATE_LIMITING=true
MAX_UPLOAD_SIZE="100M"
SESSION_TIMEOUT="24h"

# Monitoring Integration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3030
ENABLE_METRICS=true

# Critical Error Keywords (deployment will fail if found in logs)
CRITICAL_ERROR_KEYWORDS="FATAL ERROR ECONNREFUSED MODULE_NOT_FOUND ENOENT EACCES"

# Success Indicators (deployment succeeds when all are found)
SUCCESS_INDICATORS="Server is running Application started successfully Database connected ML service ready"

# Blue-Green Deployment Strategy
DEPLOYMENT_STRATEGY="blue_green"
CANARY_PERCENTAGE=0  # Not used in blue-green, but available for future
ROLLBACK_STRATEGY="instant"  # instant, gradual, manual

# Load Balancer Configuration (if using external LB)
LB_HEALTH_CHECK_PATH="/health"
LB_HEALTH_CHECK_INTERVAL=10
LB_HEALTH_CHECK_TIMEOUT=5

# Configuration Validation
# This section ensures the config file is valid
validate_config() {
    local errors=0
    
    # Check required directories exist or can be created
    for dir in "$BACKUP_DIR" "$LOG_DIR" "$UPLOAD_BASE_DIR"; do
        if [[ ! -d "$dir" ]]; then
            echo "WARNING: Directory $dir does not exist and will be created"
        fi
    done
    
    # Check required files exist
    if [[ ! -f "$NGINX_PROD_CONFIG" ]]; then
        echo "ERROR: Nginx production config not found: $NGINX_PROD_CONFIG"
        ((errors++))
    fi
    
    # Check port conflicts
    local all_ports=($BLUE_FRONTEND_PORT $BLUE_BACKEND_PORT $BLUE_ML_PORT 
                    $GREEN_FRONTEND_PORT $GREEN_BACKEND_PORT $GREEN_ML_PORT)
    local unique_ports=($(printf "%s\n" "${all_ports[@]}" | sort -u))
    
    if [[ ${#all_ports[@]} -ne ${#unique_ports[@]} ]]; then
        echo "ERROR: Port conflicts detected in configuration"
        ((errors++))
    fi
    
    return $errors
}

# Export all variables for use in deployment scripts
export BLUE_PORTS_START GREEN_PORTS_START LOCAL_PORTS_START
export BLUE_FRONTEND_PORT BLUE_BACKEND_PORT BLUE_ML_PORT
export GREEN_FRONTEND_PORT GREEN_BACKEND_PORT GREEN_ML_PORT
export DB_USER BLUE_DB_NAME GREEN_DB_NAME
export UPLOAD_BASE_DIR BLUE_UPLOAD_DIR GREEN_UPLOAD_DIR
export DOCKER_UID DOCKER_GID
export PRODUCTION_DOMAIN PRODUCTION_URL
export HEALTH_CHECK_TIMEOUT HEALTH_CHECK_RETRIES
export SERVICE_START_TIMEOUT DB_MIGRATION_TIMEOUT
export BACKUP_DIR BACKUP_RETENTION_DAYS
export MIN_DISK_SPACE_GB MIN_MEMORY_MB
export LOG_DIR
export DRY_RUN SKIP_HEALTH_CHECKS SKIP_BACKUPS VERBOSE_LOGGING