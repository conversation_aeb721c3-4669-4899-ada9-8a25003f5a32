services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-db
    environment:
      POSTGRES_USER: spheroseg
      POSTGRES_PASSWORD: spheroseg_dev
      POSTGRES_DB: spheroseg
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5
    network_mode: host
    restart: unless-stopped

  # Redis cache service
  # Used for:
  # - Optional caching to improve performance (gracefully degrades if unavailable)
  # - Future: Export queue management for large batch operations
  # - Future: WebSocket session management across multiple backend instances
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend service
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: spheroseg-frontend
    environment:
      - VITE_API_URL=http://localhost:3001
      - VITE_API_BASE_URL=http://localhost:3001/api
      - VITE_ML_SERVICE_URL=http://localhost:3001/api/ml
      - VITE_WS_URL=ws://localhost:3001
      - NODE_ENV=development
    ports:
      - "3000:5173"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - ./index.html:/app/index.html
      - ./vite.config.ts:/app/vite.config.ts
      - ./tailwind.config.ts:/app/tailwind.config.ts
      - ./postcss.config.js:/app/postcss.config.js
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.app.json:/app/tsconfig.app.json
    depends_on:
      - backend
    network_mode: host
    restart: unless-stopped

  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: ../docker/backend.Dockerfile
    container_name: spheroseg-backend
    env_file: ${ENV_FILE:-.env.development}
    environment:
      - NODE_ENV=development
      - HOST=0.0.0.0
      - PORT=3001
      - DATABASE_URL=postgresql://spheroseg:spheroseg_dev@localhost:5432/spheroseg
      - REDIS_URL=redis://localhost:6379
      - JWT_ACCESS_SECRET=b75d09c9e67acfe64cf2ff2ebe704648b2b6deba44b1eea6bed51a66b325fd41
      - JWT_REFRESH_SECRET=b1e6ae77c4da116fe524c057879c0779a7fe5f3cc26a59bbc1ab3ef482bc0a3d
      - SEGMENTATION_SERVICE_URL=http://localhost:8000
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4173,http://localhost:5173
      - WS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4173,http://localhost:5173
      - CORS_ORIGIN=http://localhost:3000,http://localhost:4173,http://localhost:5173
      - FRONTEND_URL=http://localhost:5173
      - API_BASE_URL=http://localhost:3001
      - BACKEND_URL=http://localhost:3001
      - PUBLIC_URL=http://localhost:3001
      - EMAIL_SERVICE=${EMAIL_SERVICE:-smtp}
      - FROM_EMAIL=${FROM_EMAIL}
      - FROM_NAME=${FROM_NAME:-SpheroSeg Platform}
      - SMTP_HOST=${SMTP_HOST:-localhost}
      - SMTP_PORT=${SMTP_PORT:-1025}
      - SMTP_SECURE=${SMTP_SECURE:-false}
      - SMTP_AUTH=${SMTP_AUTH:-false}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - SMTP_REQUIRE_TLS=${SMTP_REQUIRE_TLS:-false}
      - SMTP_IGNORE_TLS=${SMTP_IGNORE_TLS:-false}
      - EMAIL_ALLOW_INSECURE=${EMAIL_ALLOW_INSECURE:-false}
      - EMAIL_TIMEOUT=${EMAIL_TIMEOUT:-30000}
      - SMTP_CONNECTION_TIMEOUT_MS=${SMTP_CONNECTION_TIMEOUT_MS:-30000}
      - SMTP_GREETING_TIMEOUT_MS=${SMTP_GREETING_TIMEOUT_MS:-30000}
      - SMTP_SOCKET_TIMEOUT_MS=${SMTP_SOCKET_TIMEOUT_MS:-30000}
      - EMAIL_MAX_RETRIES=${EMAIL_MAX_RETRIES:-3}
      - EMAIL_RETRY_INITIAL_DELAY=${EMAIL_RETRY_INITIAL_DELAY:-1000}
      - EMAIL_RETRY_MAX_DELAY=${EMAIL_RETRY_MAX_DELAY:-30000}
      - EMAIL_RETRY_BACKOFF_FACTOR=${EMAIL_RETRY_BACKOFF_FACTOR:-2}
      - SKIP_EMAIL_SEND=${SKIP_EMAIL_SEND:-false}
      - SMTP_DEBUG=${SMTP_DEBUG:-false}
      - EMAIL_DEBUG=${EMAIL_DEBUG:-false}
      - REQUIRE_EMAIL_VERIFICATION=${REQUIRE_EMAIL_VERIFICATION:-false}
      # OpenTelemetry configuration
      - OTEL_SERVICE_NAME=spheroseg-backend
      - OTEL_SERVICE_VERSION=1.0.0
      - OTEL_SERVICE_NAMESPACE=spheroseg
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      - OTEL_ENABLED_EXPORTERS=jaeger,console
      - OTEL_SAMPLING_RATE=${OTEL_SAMPLING_RATE:-0.1}
      - OTEL_ENABLE_CONSOLE=true
      - OTEL_PROMETHEUS_PORT=9464
    ports:
      - "3001:3001"
    volumes:
      - ./backend/src:/app/src
      - ./backend/prisma:/app/prisma
      - ./backend/uploads:/app/uploads
      - backend-db:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      ml-service:
        condition: service_started
      jaeger:
        condition: service_started
    network_mode: host
    restart: unless-stopped

  # ML Service
  ml-service:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.Dockerfile
    container_name: spheroseg-ml
    # GPU configuration for Docker Compose v2
    runtime: nvidia
    env_file: ${ENV_FILE:-.env.development}
    ports:
      - "8000:8000"
    volumes:
      - ./backend/segmentation:/app
      - ./backend/segmentation/weights:/app/weights
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - DISPLAY=:99
      - QT_QPA_PLATFORM=offscreen
      # GPU environment variables
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
    # Resource limits for memory
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 2G
    restart: unless-stopped

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: spheroseg-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    network_mode: host
    restart: unless-stopped

  # Grafana visualization
  grafana:
    image: grafana/grafana:latest
    container_name: spheroseg-grafana
    env_file: ${ENV_FILE:-.env.development}
    ports:
      - "3030:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
    depends_on:
      - prometheus
    restart: unless-stopped

  # MailHog email testing service
  mailhog:
    image: mailhog/mailhog
    container_name: spheroseg-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    network_mode: host
    restart: unless-stopped

  # Jaeger distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: spheroseg-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
      - "9411:9411"    # Zipkin collector
      - "4317:4317"    # OTLP gRPC
      - "4318:4318"    # OTLP HTTP
    network_mode: host
    restart: unless-stopped

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  ml-models:
    driver: local
  backend-db:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local