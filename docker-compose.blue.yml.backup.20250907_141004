version: '3.8'

services:
  # Blue Frontend (port 4000)
  blue-frontend:
    build:
      context: .
      dockerfile: docker/frontend.prod.Dockerfile
      args:
        - VITE_API_BASE_URL=/api
        - VITE_ML_SERVICE_URL=/api/ml
        - VITE_WS_URL=
    container_name: blue-frontend
    ports:
      - "4000:80"
    networks:
      - blue-network
    environment:
      - NODE_ENV=production
    restart: always
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Blue Backend API (port 4001)
  blue-backend:
    build:
      context: ./backend
      dockerfile: ../docker/backend.prod.Dockerfile
    container_name: blue-backend
    ports:
      - "4001:3001"
    networks:
      - blue-network
    depends_on:
      postgres-blue:
        condition: service_healthy
      redis-blue:
        condition: service_healthy
      mailhog-blue:
        condition: service_started
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - PORT=3001
      - API_BASE_URL=https://spherosegapp.utia.cas.cz
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD}@postgres-blue:5432/spheroseg_blue
      - REDIS_URL=redis://redis-blue:6379
      - JWT_ACCESS_SECRET=${BLUE_JWT_ACCESS_SECRET}
      - JWT_REFRESH_SECRET=${BLUE_JWT_REFRESH_SECRET}
      - SEGMENTATION_SERVICE_URL=http://blue-ml:8000
      - FROM_EMAIL=<EMAIL>
      - CORS_ORIGIN=http://localhost:4000,http://localhost:4080
      - ALLOWED_ORIGINS=http://localhost:4000,http://localhost:4080
      - WS_ALLOWED_ORIGINS=http://localhost:4000,http://localhost:4080
      - FRONTEND_URL=https://spherosegapp.utia.cas.cz
      # Email configuration - UTIA SMTP
      - EMAIL_SERVICE=smtp
      - SMTP_HOST=mail.utia.cas.cz
      - SMTP_PORT=25
      - SMTP_SECURE=false
      - SMTP_AUTH=false
      - SMTP_REQUIRE_TLS=false
      - SMTP_IGNORE_TLS=true
      - SKIP_EMAIL_SEND=true  # TEMPORARY: Skip email sending due to SMTP timeout
      - UPLOAD_DIR=/app/uploads
    volumes:
      - ./backend/uploads/blue:/app/uploads
      - ./backend/data/blue:/app/data
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Blue PostgreSQL Database
  postgres-blue:
    image: postgres:15-alpine
    container_name: postgres-blue
    networks:
      - blue-network
    environment:
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=spheroseg_blue
    volumes:
      - postgres_blue_data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg_blue"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Blue Redis Cache
  redis-blue:
    image: redis:7-alpine
    container_name: redis-blue
    networks:
      - blue-network
    volumes:
      - redis_blue_data:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Blue ML Service (port 4008)
  blue-ml:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.Dockerfile
    container_name: blue-ml
    ports:
      - "4008:8000"
    networks:
      - blue-network
    depends_on:
      postgres-blue:
        condition: service_healthy
      redis-blue:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD}@postgres-blue:5432/spheroseg_blue
      - REDIS_URL=redis://redis-blue:6379
    volumes:
      - ./backend/segmentation/weights:/app/weights:ro
      - ./backend/uploads/blue:/app/uploads
    restart: always
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MailHog Email Testing Service
  mailhog-blue:
    image: mailhog/mailhog:latest
    container_name: mailhog-blue
    ports:
      - "8026:8025"  # Web UI (changed from 8025 to 8026)
      - "1026:1025"  # SMTP server (changed from 1025 to 1026)
    networks:
      - blue-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Reverse Proxy (using unified config)
  nginx-blue:
    image: nginx:alpine
    container_name: nginx-blue
    volumes:
      - ./docker/nginx/nginx.blue.local.conf:/etc/nginx/nginx.conf:ro
      # SSL certificates not needed for local development
      # - /etc/letsencrypt:/etc/letsencrypt:ro
    ports:
      - "4080:80"
      - "4443:443"
    depends_on:
      - blue-frontend
      - blue-backend
      - blue-ml
    networks:
      - blue-network
    restart: always
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  blue-network:
    driver: bridge
    name: spheroseg-blue

volumes:
  postgres_blue_data:
    name: spheroseg_postgres_blue
  redis_blue_data:
    name: spheroseg_redis_blue