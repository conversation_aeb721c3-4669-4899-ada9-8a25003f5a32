# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
test-images/
node_modules
dist
dist-ssr
*.local

# Environment variables
.env
.env.local
.env.development
.env.development.local
.env.production
.env.production.local
.env.test.local
.env.blue
.env.green
.env.staging
.env.test
.env.utia
.env.sendgrid
.sendgrid-recovery
SECURE_CREDENTIALS.md

# SSL certificates and keys
**/docker/nginx/ssl/*.key
**/docker/nginx/ssl/*.crt
**/docker/nginx/ssl/*.pem

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test results and reports
test-results/
playwright-report/
coverage/
.nyc_output/

# Security reports

# Backup files
*.bak
*.tmp
*~
security-reports/

# Database files
*.db
*.sqlite
*.sqlite3
backend/prisma/data/
backend/prisma/dev.db

# Uploads and user data
backend/uploads/
uploads/

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.pytest_cache/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE and tool directories
.claude/
.cursor/

# Build artifacts
build/
dist/
out/

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml
bun.lockb

# Model weights and large files
*.pth
*.pth.backup
*.onnx
*.h5
*.pkl
*.model

# Generated documentation
docs/api/generated/

# Test results
backend/e2e-results.json
backend/frontend-test-results.json
real-*-measurements-*.json
**/test-results/
**/coverage/

# Development tools
.vscode/settings.json

.eslintcache
.github/workflows/ci-cd.yml
