version: '3.8'

services:
  # Green Frontend (port 5000)
  green-frontend:
    build:
      context: .
      dockerfile: docker/frontend.prod.Dockerfile
      args:
        - VITE_API_BASE_URL=/api
        - VITE_ML_SERVICE_URL=/api/ml
        - VITE_WS_URL=
    container_name: green-frontend
    ports:
      - "5000:80"
    networks:
      - green-network
    environment:
      - NODE_ENV=production
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Green Backend API (port 5001)
  green-backend:
    build:
      context: ./backend
      dockerfile: ../docker/backend.prod.Dockerfile
    container_name: green-backend
    ports:
      - "5001:3001"
    networks:
      - green-network
    depends_on:
      postgres-green:
        condition: service_healthy
      redis-green:
        condition: service_healthy
    env_file: .env.green.prod
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - API_BASE_URL=https://spherosegapp.utia.cas.cz
      - DATABASE_URL=***************************************************************/spheroseg_green
      - REDIS_URL=redis://redis-green:6379
      - JWT_ACCESS_SECRET=a3f8b9c2d4e6f1a5b7c9d2e4f6a8b1c3d5e7f9a2b4c6d8e1f3a5b7c9d1e3f5a7
      - JWT_REFRESH_SECRET=b4f9c1d3e5f7a2b6c8d1e3f5a7b9c2d4e6f8a1b3c5d7e9f2a4b6c8d1e3f5a7b9
      - FROM_EMAIL=<EMAIL>
      - SEGMENTATION_SERVICE_URL=http://green-ml:8000
      - CORS_ORIGIN=https://spherosegapp.utia.cas.cz,http://***************:5000,http://localhost:5000,http://127.0.0.1:5000,http://localhost:5001,http://127.0.0.1:5001,http://localhost:5080
      - WS_ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz,wss://spherosegapp.utia.cas.cz,http://***************:5000,http://localhost:5000,http://127.0.0.1:5000,ws://localhost:5001,ws://127.0.0.1:5001,http://localhost:5080,ws://localhost:5080
      - ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz,http://***************:5000,http://localhost:5000,http://127.0.0.1:5000,http://localhost:5080
      - FRONTEND_URL=https://spherosegapp.utia.cas.cz
      - UPLOAD_DIR=/app/uploads
      # Email configuration - DISABLED for production
      - EMAIL_SERVICE=smtp
      - SMTP_HOST=localhost
      - SMTP_PORT=25
      - SMTP_SECURE=false
      - SMTP_AUTH=false
      - SMTP_REQUIRE_TLS=false
      - SMTP_USER=
      - SMTP_PASS=
      - SKIP_EMAIL_SEND=true
      - REQUIRE_EMAIL_VERIFICATION=false
      - EMAIL_ALLOW_INSECURE=true
      - EMAIL_TIMEOUT=5000
    volumes:
      - ./backend/uploads/green:/app/uploads
      - green-backend-logs:/app/logs
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Green PostgreSQL Database
  postgres-green:
    image: postgres:15-alpine
    container_name: postgres-green
    networks:
      - green-network
    environment:
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD=spheroseg_green_2024
      - POSTGRES_DB=spheroseg_green
    volumes:
      - green-postgres-data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Green Redis Cache
  redis-green:
    image: redis:7-alpine
    container_name: redis-green
    networks:
      - green-network
    volumes:
      - green-redis-data:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Green ML Service (port 5008) with GPU support
  green-ml:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.Dockerfile
    container_name: green-ml
    ports:
      - "5008:8000"
    networks:
      - green-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
        limits:
          memory: 8G
    environment:
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=production
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_DEVICE_ORDER=PCI_BUS_ID
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
    volumes:
      - ./backend/segmentation/weights:/app/weights
      - ./backend/uploads/green:/app/uploads
      - green-ml-logs:/app/logs
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 20s
      retries: 5
      start_period: 120s

  # Nginx Reverse Proxy for GREEN
  nginx-green:
    image: nginx:alpine
    container_name: nginx-green
    volumes:
      - ./docker/nginx/nginx.green.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "5080:80"
    depends_on:
      - green-frontend
      - green-backend
      - green-ml
    networks:
      - green-network
    restart: always
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  green-network:
    driver: bridge

volumes:
  green-postgres-data:
  green-redis-data:
  green-backend-logs:
  green-ml-logs: