{
  "$schema": "https://json.schemastore.org/tsconfig",
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" },
    { "path": "./tsconfig.test.json" }
  ],
  "compilerOptions": {
    // Base configuration shared across all sub-configs
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },

    // Core Type Safety (ENABLED - Production Ready)
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false, // TODO: Enable in phase 2 migration

    // Code Quality (ENABLED)
    "exactOptionalPropertyTypes": false, // TODO: Enable in phase 2 migration
    "noPropertyAccessFromIndexSignature": false, // TODO: Enable in phase 2 migration

    // Development Experience
    "skipLibCheck": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,

    // Legacy Support (Will be removed in future phases)
    "noUnusedLocals": false, // TODO: Enable in phase 3
    "noUnusedParameters": false // TODO: Enable in phase 3
  }
}
