{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "composite": true,
    // Build Configuration
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    // Bundler Configuration (Vite)
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    // Type Safety - Graduated Strict Mode (Phase 1)
    "strict": false, // Keep false - using granular control instead

    // Core Safety (ENABLED - Required for production)
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "strictBindCallApply": true,
    "strictFunctionTypes": true,

    // Advanced Safety (Phase 2 - TODO)
    "noUncheckedIndexedAccess": false, // Enable after fixing existing code
    "exactOptionalPropertyTypes": false, // Enable after DOM lib compatibility check
    "noPropertyAccessFromIndexSignature": false, // Enable after refactoring dynamic access

    // Code Quality (Phase 3 - TODO)
    "noUnusedLocals": false, // Enable after cleanup
    "noUnusedParameters": false, // Enable after cleanup
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,

    // Path Configuration
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"],
  "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"]
}
