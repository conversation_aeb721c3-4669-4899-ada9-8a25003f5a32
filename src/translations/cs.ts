export default {
  common: {
    appName: 'Segmentace Sféroidů',
    loading: 'Na<PERSON><PERSON><PERSON>ání...',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    cancel: '<PERSON><PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON><PERSON>',
    edit: 'Upravit',
    create: 'Vyt<PERSON>řit',
    search: 'Hledat',
    error: '<PERSON><PERSON><PERSON>',
    success: '<PERSON><PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON><PERSON><PERSON>',
    signIn: 'Přihlásit se',
    signUp: 'Registrovat se',
    signOut: 'Odhlásit se',
    settings: 'Nastavení',
    profile: 'Profil',
    dashboard: '<PERSON><PERSON>eh<PERSON>',
    project: 'Projekt',
    projects: 'Projekty',
    polygon: 'Polygon',
    newProject: 'Nový projekt',
    upload: 'Nahrát',
    uploadImages: '<PERSON>r<PERSON><PERSON> obr<PERSON>zky',
    recentAnalyses: 'Nedávné analýzy',
    noProjects: 'Nebyly nalezeny žádné projekty',
    noImages: 'Nebyly nalezeny žádn<PERSON> o<PERSON>r<PERSON>',
    createYourFirst: 'Vyt<PERSON><PERSON>te svůj první projekt pro začátek',
    tryAgain: '<PERSON><PERSON><PERSON> znovu',
    email: '<PERSON><PERSON>',
    password: '<PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON>no',
    description: 'Popis',
    date: 'Datum',
    status: 'Stav',
    images: 'Obrázky',
    image: 'Obrázek',
    projectName: 'Název projektu',
    projectDescription: 'Popis projektu',
    theme: 'Motiv',
    language: 'Jazyk',
    light: 'Světlý',
    dark: 'Tmavý',
    system: 'Systémový',
    welcome: 'Vítejte v platformě pro segmentaci sféroidů',
    account: 'Účet',
    notifications: 'Oznámení',
    passwordConfirm: 'Potvrdit heslo',
    manageAccount: 'Spravovat účet',
    getStarted: 'Začít',
    learnMore: 'Zjistit více',
    documentation: 'Dokumentace',
    changePassword: 'Změnit heslo',
    deleteAccount: 'Smazat účet',
    termsOfService: 'Podmínky služby',
    privacyPolicy: 'Zásady ochrany osobních údajů',
    createAccount: 'Vytvořit účet',
    signInToAccount: 'Přihlásit se k účtu',
    sort: 'Řadit',
    no_preview: 'Žádný náhled',
    openMenu: 'Otevřít menu',
    logOut: 'Odhlásit se',
    pageNotFound: 'Ojoj! Stránka nebyla nalezena',
    returnToHome: 'Návrat domů',
    next: 'Další',
    copy: 'Kopírovat',
    close: 'Zavřít',
    noImage: 'Žádný obrázek',
    untitledImage: 'Nepojmenovaný obrázek',
    rename: 'Přejmenovat',
    redirectingToDashboard: 'Přesměrování na přehled...',
  },
  landing: {
    hero: {
      badge: 'Pokročilá platforma pro segmentaci sféroidů',
      title: 'AI analýza buněk pro biomedicínský výzkum',
      subtitle:
        'Vylepšete svou analýzu mikroskopických buněčných obrázků s naší nejmodernější platformou pro segmentaci sféroidů. Navrženo pro výzkumníky hledající přesnost a efektivitu.',
      getStarted: 'Začít',
      learnMore: 'Zjistit více',
    },
    about: {
      badge: 'Naše mise',
      title: 'Posouvání biomedicínského výzkumu prostřednictvím technologie',
      description1:
        'Naše platforma byla vyvinuta Bc. Michalem Průškem, studentem Fakulty jaderné a fyzikálně inženýrské ČVUT v Praze, pod vedením Ing. Adama Novozámského, Ph.D.',
      description2:
        'Tento projekt je ve spolupráci s výzkumníky z Ústavu biochemie a mikrobiologie UCT Praha (VŠCHT Praha).',
      description3:
        'Kombinujeme nejmodernější AI modely s intuitivním rozhraním a poskytujeme výzkumníkům mocné nástroje pro analýzu mikroskopických obrázků se zaměřením na segmentaci sféroidů s bezkonkurenční přesností.',
      contactText: 'Pro dotazy nás prosím kontaktujte na',
    },
    cta: {
      title:
        'Jste připraveni transformovat svůj pracovní postup analýzy buněk?',
      subtitle:
        'Připojte se k předním výzkumníkům, kteří již používají naši platformu k urychlení svých objevů.',
      cardTitle: 'Začněte ještě dnes',
      cardDescription:
        'Zaregistrujte se zdarma a zažijte sílu AI-řízené segmentace sféroidů.',
      createAccount: 'Vytvořit účet',
    },
    features: {
      badge: 'Výkonné možnosti',
      title: 'Pokročilé nástroje pro biomedicínský výzkum',
      subtitle:
        'Naše platforma nabízí komplexní sadu funkcí navržených pro zefektivnění vašeho pracovního postupu segmentace sféroidů.',
      cards: {
        advancedSegmentation: {
          title: 'Pokročilá segmentace',
          description:
            'Přesná detekce sféroidů s analýzou hranic pro přesné měření buněk.',
        },
        aiPowered: {
          title: 'AI analýza',
          description:
            'Využijte algoritmy hlubokého učení pro automatickou detekci a klasifikaci buněk.',
        },
        effortlessUploads: {
          title: 'Snadné nahrávání',
          description:
            'Přetáhněte své mikroskopické obrázky pro okamžité zpracování a analýzu.',
        },
        statisticalInsights: {
          title: 'Statistické přehledy',
          description:
            'Komplexní metriky a vizualizace pro extrakci významných datových vzorců.',
        },
        collaboration: {
          title: 'Nástroje pro spolupráci',
          description:
            'Sdílejte projekty s kolegy a spolupracujte v reálném čase na výzkumných nálezech.',
        },
        processingPipeline: {
          title: 'Zpracovatelský proces',
          description:
            'Automatizovaný pracovní postup od předzpracování po finální analýzu s přizpůsobitelnými parametry.',
        },
      },
    },
  },
  dashboard: {
    manageProjects: 'Spravujte své výzkumné projekty a analýzy',
    projectGallery: 'Galerie Projektů',
    projectGalleryDescription:
      'Procházejte a spravujte všechny své segmentační projekty',
    statsOverview: 'Přehled statistik',
    totalProjects: 'Celkem projektů',
    activeProjects: 'Aktivní projekty',
    totalImages: 'Celkem obrázků',
    totalAnalyses: 'Celkem analýz',
    lastUpdated: 'Naposledy aktualizováno',
    noProjectsDescription:
      'Zatím jste nevytvořili žádný projekt. Vytvořte svůj první projekt pro začátek.',
    noImagesDescription: 'Nahrajte několik obrázků pro začátek',
    searchProjectsPlaceholder: 'Hledat projekty...',
    searchImagesPlaceholder: 'Hledat obrázky podle názvu...',
    sortBy: 'Řadit podle',
    name: 'Název',
    lastChange: 'Poslední změna',
    status: 'Stav',
    stats: {
      totalProjects: 'Celkem projektů',
      totalProjectsDesc: 'Aktivní studie sféroidů',
      processedImages: 'Zpracované obrázky',
      processedImagesDesc: 'Úspěšně segmentovány',
      uploadedToday: 'Nahrané dnes',
      uploadedTodayDesc: 'Obrázky sféroidů',
      storageUsed: 'Využité úložiště',
      totalSpaceUsed: 'Celkem využitého místa',
    },
    completed: 'Dokončeno',
    processing: 'Zpracování',
    pending: 'Čekající',
    failed: 'Selhalo',
    storageUsed: 'Využité úložiště',
  },
  projects: {
    createProject: 'Vytvořit nový projekt',
    createProjectDesc:
      'Přidat nový projekt pro organizaci vašich sféroidních snímků a analýz.',
    projectNamePlaceholder: 'např. HeLa buněčné sferoidy',
    projectDescPlaceholder:
      'např. Analýza nádorových sféroidů pro studie rezistence na léky',
    creatingProject: 'Vytváření...',
    duplicateProject: 'Duplikovat',
    shareProject: 'Sdílet',
    deleteProject: 'Smazat',
    openProject: 'Otevřít projekt',
    confirmDelete: 'Opravdu chcete tento projekt smazat?',
    projectCreated: 'Projekt byl úspěšně vytvořen',
    projectDeleted: 'Projekt byl úspěšně smazán',
    viewProject: 'Zobrazit projekt',
    projectImages: 'Obrázky projektu',
    noProjects: 'Žádné projekty nebyly nalezeny',
    imageDeleted: 'Obrázek byl odstraněn',
    deleteImageError: 'Nepodařilo se odstranit obrázek',
    deleteImageFailed: 'Odstranění obrázku selhalo',
    imagesQueuedForSegmentation:
      '{{count}} obrázků přidáno do fronty pro segmentaci',
    imageQueuedForResegmentation: 'Obrázek přidán do fronty pro re-segmentaci',
    allImagesAlreadySegmented:
      'Všechny obrázky jsou již segmentovány nebo ve frontě',
    errorAddingToQueue: 'Chyba při přidávání obrázků do fronty',
    imageAlreadyProcessing: 'Obrázek je již zpracováván',
    processImageFailed: 'Nepodařilo se zpracovat obrázek',
    selected: '{{count}} obrázek vybrán',
    deleteSelected: 'Smazat vybrané',
    segmentationCompleted: 'Segmentace dokončena pro obrázek',
    segmentationFailed: 'Segmentace selhala',
    segmentationStarted: 'Segmentace byla zahájena',
    segmentationCompleteWithCount:
      'Segmentace dokončena! Nalezeno {{count}} objektů',
    failedToLoadProjects: 'Nepodařilo se načíst projekty',
    projectNameRequired: 'Zadejte prosím název projektu',
    mustBeLoggedIn: 'Pro vytvoření projektu se musíte přihlásit',
    failedToCreateProject: 'Nepodařilo se vytvořit projekt',
    serverResponseInvalid: 'Odpověď serveru byla neplatná',
    projectCreatedDesc: '"{{name}}" je připraven pro obrázky',
    descriptionOptional: 'Popis (volitelný)',
    noDescriptionProvided: 'Nebyl poskytnut žádný popis',
    deleteDialog: {
      title: 'Potvrdit smazání',
      description:
        'Opravdu chcete smazat {{count}} vybraných obrázků? Tuto akci nelze vrátit zpět.',
    },
    selectProject: 'Vybrat projekt',
    projectSelection: 'Výběr projektu',
    selectProjectHeader: 'Vybrat projekt',
  },
  errors: {
    noProjectOrUser:
      'Není vybrán žádný projekt nebo uživatel. Vyberte prosím projekt ze seznamu.',
    unknown: 'Nastala neočekávaná chyba. Zkuste prosím akci opakovat.',
    network:
      'Nelze se připojit k serveru. Zkontrolujte své internetové připojení a zkuste to znovu.',
    unauthorized: 'Vaše přihlášení vypršelo. Přihlaste se prosím znovu.',
    forbidden:
      'K této akci nemáte oprávnění. Kontaktujte správce, pokud si myslíte, že je to chyba.',
    notFound: 'Požadovaný obsah nebyl nalezen. Možná byl smazán nebo přesunut.',
    conflict:
      'Tento email je již zaregistrován. Zkuste se přihlásit nebo použijte jiný email.',
    invalidCredentials:
      'Nesprávný email nebo heslo. Zkontrolujte své přihlašovací údaje.',
    validation:
      'Zadané údaje nejsou správné. Zkontrolujte formulář a opravte chyby.',
    general: 'Něco se pokazilo. Zkuste to prosím znovu za chvíli.',
    server: 'Server je momentálně nedostupný. Zkuste to prosím později.',
    timeout:
      'Požadavek trval příliš dlouho. Zkontrolujte připojení a zkuste to znovu.',
    sessionExpired:
      'Vaše přihlášení vypršelo. Pro pokračování se prosím přihlaste znovu.',
    tooManyRequests:
      'Příliš mnoho požadavků. Počkejte prosím chvíli a zkuste to znovu.',
    serverUnavailable:
      'Služba je dočasně nedostupná. Zkuste to prosím za několik minut.',
    clientError:
      'Chyba v požadavku. Zkontrolujte zadané údaje a zkuste to znovu.',
    emailAlreadyExists:
      'Tento email je již zaregistrován. Zkuste se přihlásit nebo použijte jiný email.',
    validationErrors: {
      projectNameRequired: 'Zadejte prosím název projektu',
      loginRequired: 'Pro vytvoření projektu se musíte přihlásit',
      emailRequired: 'E-mail je povinný',
      passwordRequired: 'Heslo je povinné',
      invalidEmail: 'Zadejte prosím platnou e-mailovou adresu',
      passwordTooShort: 'Heslo musí mít alespoň 6 znaků',
      passwordsDoNotMatch: 'Hesla se neshodují',
      confirmationRequired: 'Potvrďte prosím svou akci',
      fieldRequired: 'Toto pole je povinné',
    },
    operations: {
      loadProject:
        'Nepodařilo se načíst projekt. Zkontrolujte připojení a zkuste to znovu.',
      saveProject:
        'Nepodařilo se uložit změny projektu. Zkuste to prosím znovu.',
      uploadImage:
        'Nepodařilo se nahrát obrázek. Zkontrolujte formát a velikost souboru.',
      deleteImage:
        'Nelze smazat obrázek. Zkuste obnovit stránku a opakovat akci.',
      processImage:
        'Zpracování obrázku selhalo. Zkuste jiný obrázek nebo kontaktujte podporu.',
      segmentation:
        'Segmentace selhala. Zkuste použít jiný model nebo upravit nastavení.',
      export: 'Export dat selhal. Zkontrolujte, zda jsou data k dispozici.',
      login: 'Přihlášení selhalo. Zkontrolujte email a heslo.',
      logout: 'Odhlášení selhalo. Zkuste zavřít prohlížeč.',
      register: 'Registrace selhala. Tento email možná již používá někdo jiný.',
      updateProfile:
        'Nepodařilo se aktualizovat profil. Zkontrolujte zadané údaje.',
      changePassword:
        'Nepodařilo se změnit heslo. Zkontrolujte současné heslo.',
      deleteAccount:
        'Nepodařilo se smazat účet. Kontaktujte podporu pro pomoc.',
      resetPassword:
        'Reset hesla selhal. Zkontrolujte zadanou emailovou adresu.',
      updateConsent:
        'Nepodařilo se aktualizovat nastavení souhlasu. Zkuste to prosím znovu.',
      unshareProject: 'Nepodařilo se odebrat projekt ze sdílených projektů',
      deleteProject: 'Nepodařilo se smazat projekt',
    },
    deleteImages: 'Nepodařilo se smazat vybrané obrázky',
    contexts: {
      dashboard: 'Chyba dashboardu',
      project: 'Chyba projektu',
      image: 'Chyba obrázku',
      segmentation: 'Chyba segmentace',
      export: 'Chyba exportu',
      auth: 'Chyba autentifikace',
      profile: 'Chyba profilu',
      settings: 'Chyba nastavení',
    },
  },
  images: {
    uploadImages: 'Nahrát obrázky',
    dragDrop: 'Přetáhněte obrázky sem',
    clickToSelect: 'nebo klikněte pro výběr souborů',
    acceptedFormats: 'Podporované formáty: JPEG, PNG, TIFF, BMP (max 10MB)',
    uploadProgress: 'Průběh nahrávání',
    uploadingTo: 'Nahrávání do',
    currentProject: 'Aktuální projekt',
    autoSegment: 'Automatická segmentace obrázků po nahrání',
    uploadCompleted: 'Nahrávání dokončeno',
    uploadFailed: 'Nahrávání selhalo',
    imagesUploaded: 'Obrázky byly úspěšně nahrány',
    imagesFailed: 'Nahrávání obrázků selhalo',
    viewAnalyses: 'Zobrazit analýzy',
    noAnalysesYet: 'Zatím žádné analýzy',
    runAnalysis: 'Spustit analýzu',
    viewResults: 'Zobrazit výsledky',
    dropImagesHere: 'Přetáhněte obrázky sem...',
    selectProjectFirst: 'Nejprve vyberte projekt',
    projectRequired: 'Před nahráním obrázků musíte vybrat projekt',
    pending: 'Čekající',
    uploading: 'Nahrávání',
    processing: 'Zpracování',
    complete: 'Dokončeno',
    error: 'Chyba',
    imageDeleted: 'Obrázek byl úspěšně smazán',
    deleteImageFailed: 'Smazání obrázku selhalo',
    deleteImageError: 'Chyba při mazání obrázku',
    imageAlreadyProcessing: 'Obrázek se již zpracovává',
    processImageFailed: 'Zpracování obrázku selhalo',
  },
  settings: {
    pageTitle: 'Nastavení',
    profile: 'Profil',
    account: 'Účet',
    models: 'Modely',
    manageSettings: 'Spravujte své nastavení účtu',
    appearance: 'Vzhled',
    themeSettings: 'Nastavení motivu',
    systemDefault: 'Systémové výchozí',
    languageSettings: 'Nastavení jazyka',
    selectLanguage: 'Vyberte jazyk',
    accountSettings: 'Nastavení účtu',
    notificationSettings: 'Nastavení oznámení',
    emailNotifications: 'E-mailová oznámení',
    pushNotifications: 'Push oznámení',
    profileSettings: 'Nastavení profilu',
    profileUpdated: 'Profil byl úspěšně aktualizován',
    profileUpdateFailed: 'Aktualizace profilu selhala',
    saveChanges: 'Uložit změny',
    savingChanges: 'Ukládání změn...',
    notifications: {
      projectUpdates: 'Aktualizace projektů',
      analysisCompleted: 'Analýza dokončena',
      newFeatures: 'Nové funkce',
      marketingEmails: 'Marketingové e-maily',
      billing: 'Oznámení o fakturaci',
    },
    modelSelection: {
      title: 'Výběr modelu',
      description: 'Vyberte AI model pro segmentaci buněk',
      models: {
        hrnet: {
          name: 'HRNet',
          description: 'Rychlý a efektivní model pro segmentaci v reálném čase',
        },
        cbam: {
          name: 'CBAM-ResUNet',
          description: 'Přesný segmentační model s mechanismy pozornosti',
        },
        unet_spherohq: {
          name: 'UNet (SpheroHQ)',
          description:
            'Nejlepší výkon na datové sadě SpheroHQ - optimalizováno pro segmentaci sféroidů s vyváženou rychlostí a přesností (~0.25s/obr., 10 obr./s)',
        },
      },
    },
    confidenceThreshold: 'Práh Spolehlivosti',
    confidenceThresholdDescription:
      'Minimální spolehlivost požadovaná pro predikce segmentace',
    detectHoles: 'Detekce Děr',
    detectHolesDescription:
      'Povolit detekci vnitřních struktur a děr uvnitř buněk',
    currentThreshold: 'Současný práh',
    modelSelected: 'Model úspěšně vybrán',
    modelSettingsSaved: 'Nastavení modelu úspěšně uloženo',
    modelSize: {
      small: 'Malý',
      medium: 'Střední',
      large: 'Velký',
    },
    modelDescription: {
      hrnet:
        'Vyvážený model s dobrou rychlostí a kvalitou (E2E ~309ms, 4.9 obr/s)',
      cbam_resunet:
        'Nejpřesnější segmentace s mechanismy pozornosti (E2E ~482ms, 2.7 obr/s)',
      unet_spherohq:
        'Nejrychlejší model po optimalizacích! Výborný pro zpracování v reálném čase (E2E ~286ms, 5.5 obr/s)',
    },
    dataUsageTitle: 'Použití dat a soukromí',
    dataUsageDescription:
      'Kontrola použití vašich dat pro strojové učení a výzkum',
    allowMLTraining: {
      label: 'Povolit trénování ML modelů',
      description:
        'Povolit použití vašich dat pro trénování a zlepšování našich segmentačních modelů',
    },
    consent: {
      privacyNotice:
        'Vaše soukromí je pro nás důležité. Tato nastavení řídí, jak mohou být vaše nahrané obrázky a segmentační data použity ke zlepšení našich ML modelů. Tyto preference můžete kdykoli změnit.',
      dataUsageNote:
        'Data od uživatelů, kteří se odhlásili, nebudou zahrnuta do žádných tréninkových procesů.',
      algorithmImprovement: {
        label: 'Vylepšení algoritmů',
        description: 'Použít data pro zvýšení přesnosti a rychlosti segmentace',
      },
      featureDevelopment: {
        label: 'Vývoj funkcí',
        description: 'Pomoci vyvinout nové funkce a schopnosti',
      },
      lastUpdated: 'Naposledy aktualizováno',
      savePreferences: 'Uložit preference souhlasu',
      savingPreferences: 'Ukládání...',
    },
    cancel: 'Zrušit',
    deleting: 'Mazání...',
    deleteAccount: 'Smazat účet',
    accountDeleted: 'Účet byl úspěšně smazán',
    deleteAccountError: 'Nepodařilo se smazat účet',
    deleteAccountDialog: {
      title: 'Smazat účet',
      description:
        'Tuto akci nelze vrátit zpět. Tímto trvale smažete svůj účet a odstraníte všechna svá data z našich serverů.',
      whatWillBeDeleted: 'Co bude smazáno:',
      deleteItems: {
        account: 'Váš uživatelský účet a profil',
        projects: 'Všechny vaše projekty a obrázky',
        segmentation: 'Všechna data segmentace a výsledky',
        settings: 'Nastavení účtu a preference',
      },
      confirmationLabel: 'Pro potvrzení prosím napište {email}:',
      confirmationPlaceholder: '{email}',
    },
    personal: 'Osobní informace',
    fullName: 'Celé jméno',
    organization: 'Organizace',
    department: 'Oddělení',
    publicProfile: 'Veřejný profil',
    bio: 'Biografie',
    makeProfileVisible: 'Učinit můj profil viditelným pro ostatní výzkumníky',
    dangerZone: 'Nebezpečná zóna',
    deleteAccountWarning:
      'Jakmile svůj účet smažete, není cesty zpět. Všechna vaše data budou trvale smazána.',
    currentPassword: 'Současné heslo',
    newPassword: 'Nové heslo',
    confirmNewPassword: 'Potvrdit nové heslo',
    fillAllFields: 'Prosím vyplňte všechna povinná pole',
    passwordsDoNotMatch: 'Hesla se neshodují',
    passwordTooShort: 'Heslo musí mít alespoň 6 znaků',
    passwordChanged: 'Heslo bylo úspěšně změněno',
    passwordsMatch: 'Hesla se shodují',
    changingPassword: 'Měním heslo...',
    changePassword: 'Změnit heslo',
    languageUpdated: 'Jazyk byl úspěšně aktualizován',
    themeUpdated: 'Motiv byl úspěšně aktualizován',
    appearanceDescription: 'Přizpůsobte vzhled aplikace',
    language: 'Jazyk',
    languageDescription: 'Vyberte svůj preferovaný jazyk',
    theme: 'Motiv',
    themeDescription: 'Vyberte světlý, tmavý nebo systémový motiv',
    light: 'Světlý',
    dark: 'Tmavý',
    system: 'Systémový',
  },
  segmentation: {
    mode: {
      view: 'Zobrazit a navigovat',
      edit: 'Upravit',
      editVertices: 'Upravit vrcholy',
      addPoints: 'Přidat body',
      create: 'Vytvořit',
      createPolygon: 'Vytvořit polygon',
      slice: 'Rozřezat',
      delete: 'Smazat',
      deletePolygon: 'Smazat polygon',
      unknown: 'Neznámý',
    },
    modeDescription: {
      view: 'Procházet a vybírat polygony',
      edit: 'Přesunout a upravit vrcholy',
      addPoints: 'Přidat body mezi vrcholy',
      create: 'Vytvořit nové polygony',
      slice: 'Rozdělit polygony čarou',
      delete: 'Odstranit polygony',
    },
    toolbar: {
      mode: 'Režim',
      keyboard: 'Klávesa: {{key}}',
      requiresSelection: 'Vyžaduje výběr polygonu',
      requiresPolygonSelection: 'Vyžaduje výběr polygonu',
      select: 'Vybrat',
      undoTooltip: 'Zpět (Ctrl+Z)',
      undo: 'Zpět',
      redoTooltip: 'Znovu (Ctrl+Y)',
      redo: 'Znovu',
      zoomInTooltip: 'Přiblížit (+)',
      zoomIn: 'Přiblížit',
      zoomOutTooltip: 'Oddálit (-)',
      zoomOut: 'Oddálit',
      resetViewTooltip: 'Resetovat pohled (R)',
      resetView: 'Reset',
      unsavedChanges: 'Neuložené změny',
      saving: 'Ukládání...',
      save: 'Uložit',
      keyboardShortcuts:
        'V: Zobrazit • E: Upravit • A: Přidat • N: Nový • S: Rozřezat • D: Smazat',
      nothingToSave: 'Všechny změny uloženy',
    },
    status: {
      polygons: 'polygonů',
      vertices: 'vrcholů',
      visible: 'viditelných',
      hidden: 'skrytých',
      selected: 'vybrán',
      saved: 'Uloženo',
      unsaved: 'Neuloženo',
      noPolygons: 'Žádné polygony',
      startCreating: 'Začněte vytvářením polygonu',
      polygonList: 'Seznam polygonů',
      external: 'Externí',
      internal: 'Interní',
    },
    shortcuts: {
      buttonText: 'Zkratky',
      title: 'Klávesové zkratky',
      dialogTitle: 'Klávesové zkratky',
      footerNote:
        'Tyto zkratky fungují v editoru segmentace pro rychlejší a pohodlnější práci.',

      // Categories
      categories: {
        modes: 'Režimy úprav',
        actions: 'Akce',
        view: 'Ovládání pohledu',
        navigation: 'Navigace',
      },

      // Mode shortcuts
      viewMode: 'Režim zobrazení',
      editVertices: 'Režim úpravy vrcholů',
      addPoints: 'Režim přidávání bodů',
      createPolygon: 'Vytvořit nový polygon',
      sliceMode: 'Režim řezání',
      deleteMode: 'Režim mazání',

      // Action shortcuts
      save: 'Uložit',
      undo: 'Zpět',
      redo: 'Znovu',
      deleteSelected: 'Smazat vybraný polygon',

      // View shortcuts
      zoom: 'Přiblížit/oddálit',
      resetView: 'Resetovat pohled',
      fitToScreen: 'Přizpůsobit obrazovce',

      // Navigation shortcuts
      cycleModes: 'Procházet režimy',
      cycleModesReverse: 'Procházet režimy (zpět)',
      cancel: 'Zrušit aktuální operaci',
      showHelp: 'Zobrazit tuto nápovědu',

      // Conditions
      requiresSelection: 'Vyžaduje výběr polygonu',

      // Legacy keys (kept for backward compatibility)
      v: 'Režim zobrazení',
      e: 'Režim úpravy vrcholů',
      a: 'Režim přidávání bodů',
      n: 'Vytvořit nový polygon',
      s: 'Režim řezání',
      d: 'Režim mazání',
      shift: 'Držet pro automatické přidávání bodů',
      ctrlZ: 'Zpět',
      ctrlY: 'Znovu',
      delete: 'Smazat vybraný polygon',
      esc: 'Zrušit aktuální operaci',
      plus: 'Přiblížit',
      minus: 'Oddálit',
      r: 'Resetovat pohled',
    },
    tips: {
      header: 'Tipy:',
      edit: {
        createPoint: 'Klikněte pro vytvoření nového bodu',
        holdShift: 'Držte Shift pro automatické vytváření sekvence bodů',
        closePolygon: 'Uzavřete polygon kliknutím na první bod',
      },
      slice: {
        startSlice: 'Klikněte pro zahájení řezání',
        endSlice: 'Klikněte znovu pro dokončení řezání',
        cancelSlice: 'Esc zruší řezání',
      },
      addPoints: {
        hoverLine: 'Namiřte kurzor na čáru polygonu',
        clickAdd: 'Klikněte pro přidání bodu do vybraného polygonu',
        escCancel: 'Esc ukončí režim přidávání',
      },
    },
    helpTips: {
      editMode: [
        'Klikněte pro vytvoření nového bodu',
        'Držte Shift pro automatické vytváření sekvence bodů',
        'Uzavřete polygon kliknutím na první bod',
      ],
      slicingMode: [
        'Klikněte pro zahájení řezání',
        'Klikněte znovu pro dokončení řezání',
        'Esc zruší řezání',
      ],
      pointAddingMode: [
        'Namiřte kurzor na čáru polygonu',
        'Klikněte pro přidání bodu do vybraného polygonu',
        'Esc ukončí režim přidávání',
      ],
    },
    loading: 'Načítání segmentace...',
    noPolygons: 'Nebyly nalezeny žádné polygony',
    polygonNotFound: 'Polygon nebyl nalezen',
    invalidSlice: 'Neplatná operace řezání',
    sliceSuccess: 'Polygon byl úspěšně rozřezán',
    sliceFailed: 'Řezání polygonu selhalo',
    instructions: {
      slice: {
        selectPolygon: '1. Klikněte na polygon pro jeho výběr k řezání',
        placeFirstPoint: '2. Klikněte pro umístění prvního bodu řezání',
        placeSecondPoint:
          '3. Klikněte pro umístění druhého bodu řezání a provedení řezu',
        cancel: 'Stiskněte ESC pro zrušení',
      },
      create: {
        startPolygon: '1. Klikněte pro zahájení vytváření polygonu',
        continuePoints:
          '2. Pokračujte klikáním pro přidání dalších bodů (potřeba minimálně 3)',
        finishPolygon:
          '3. Pokračujte v přidávání bodů nebo klikněte blízko prvního bodu pro uzavření polygonu',
        holdShift: 'Držte SHIFT pro automatické přidávání bodů',
        cancel: 'Stiskněte ESC pro zrušení',
      },
      addPoints: {
        clickVertex: 'Klikněte na jakýkoli vrchol pro zahájení přidávání bodů',
        addPoints:
          'Klikněte pro přidání bodů, poté klikněte na jiný vrchol pro dokončení. Klikněte přímo na jiný vrchol bez přidávání bodů pro odstranění všech bodů mezi nimi.',
        holdShift: 'Držte SHIFT pro automatické přidávání bodů',
        cancel: 'Stiskněte ESC pro zrušení',
      },
      editVertices: {
        selectPolygon: 'Klikněte na polygon pro jeho výběr k úpravě',
        dragVertices: 'Klikněte a táhněte vrcholy pro jejich přesunutí',
        addPoints: 'Držte SHIFT a klikněte na vrchol pro přidání bodů',
        deleteVertex: 'Dvojklik na vrchol pro jeho smazání',
      },
      deletePolygon: {
        clickToDelete: 'Klikněte na polygon pro jeho smazání',
      },
      view: {
        selectPolygon: 'Klikněte na polygon pro jeho výběr',
        navigation: 'Táhněte pro posouvání • Rolujte pro zvětšování',
      },
      modes: {
        slice: 'Režim řezání',
        create: 'Režim vytváření polygonu',
        addPoints: 'Režim přidávání bodů',
        editVertices: 'Režim úpravy vrcholů',
        deletePolygon: 'Režim mazání polygonu',
        view: 'Režim zobrazení',
      },
      shiftIndicator: '⚡ SHIFT: Automatické přidávání bodů',
    },
  },
  auth: {
    signIn: 'Přihlásit se',
    signUp: 'Registrovat se',
    redirectingToDashboard: 'Přesměrování na nástěnku...',
    signOut: 'Odhlásit se',
    forgotPassword: 'Zapomněli jste heslo?',
    resetPassword: 'Obnovit heslo',
    dontHaveAccount: 'Nemáte účet?',
    alreadyHaveAccount: 'Již máte účet?',
    signInWith: 'Přihlásit se pomocí',
    signUpWith: 'Registrovat se pomocí',
    orContinueWith: 'nebo pokračujte s',
    rememberMe: 'Zapamatovat si mě',
    emailRequired: 'E-mail je povinný',
    passwordRequired: 'Heslo je povinné',
    invalidEmail: 'Neplatná e-mailová adresa',
    passwordTooShort: 'Heslo musí mít alespoň 6 znaků',
    passwordsDontMatch: 'Hesla se neshodují',
    successfulSignIn: 'Úspěšné přihlášení',
    successfulSignUp: 'Úspěšná registrace',
    verifyEmail: 'Zkontrolujte prosím svůj e-mail pro potvrzení účtu',
    successfulSignOut: 'Úspěšné odhlášení',
    checkingAuthentication: 'Kontrola ověření...',
    loadingAccount: 'Načítání vašeho účtu...',
    processingRequest: 'Zpracování vašeho požadavku...',
    signInToAccount: 'Přihlaste se ke svému účtu',
    accessPlatform: 'Přístup k platformě pro segmentaci sféroidů',
    emailAddress: 'E-mailová adresa',
    emailPlaceholder: '<EMAIL>',
    password: 'Heslo',
    passwordPlaceholder: '••••••••',
    signingIn: 'Přihlašování...',
    redirectingToSignIn: 'Přesměrování k přihlášení...',
    fillAllFields: 'Vyplňte prosím všechna pole',
    signInSuccess: 'Úspěšně přihlášen',
    signInFailed: 'Přihlášení selhalo',
    registrationSuccess: 'Registrace úspěšná',
    registrationFailed: 'Registrace selhala',
    logoutFailed: 'Odhlášení selhalo',
    profileUpdateFailed: 'Aktualizace profilu selhala',
    welcomeMessage: 'Vítejte na platformě pro segmentaci sféroidů',
    confirmationRequired:
      'Potvrzovací text je povinný a musí se shodovat s vaší e-mailovou adresou',
    agreeToTerms: 'Přihlášením souhlasíte s našimi',
    termsOfService: 'Podmínkami služby',
    and: 'a',
    privacyPolicy: 'Zásadami ochrany osobních údajů',
    createAccount: 'Vytvořte svůj účet',
    signUpPlatform:
      'Zaregistrujte se pro použití platformy pro segmentaci sféroidů',
    confirmPassword: 'Potvrdit heslo',
    passwordsMatch: 'Hesla se shodují',
    passwordsDoNotMatch: 'Hesla se neshodují',
    agreeToTermsCheckbox: 'Souhlasím s',
    mustAgreeToTerms: 'Musíte souhlasit s podmínkami a ujednáními',
    creatingAccount: 'Vytváření účtu...',
    alreadyLoggedIn: 'Již jste přihlášeni',
    alreadySignedUp: 'Již jste zaregistrováni a přihlášeni.',
    goToDashboard: 'Přejít na Dashboard',
    signUpFailed: 'Registrace selhala',
    enterEmailForReset: 'Zadejte svou e-mailovou adresu pro reset hesla',
    sending: 'Odesílání...',
    sendNewPassword: 'Odeslat nové heslo',
    emailSent: 'Email odeslán',
    checkEmailForNewPassword:
      'Zkontrolujte svůj email pro odkaz na reset hesla',
    resetPasswordEmailSent:
      'Pokud email existuje, byl odeslán odkaz na reset hesla',
    resetPasswordError: 'Nepodařilo se odeslat email s novým heslem',
    backToSignIn: 'Zpět na přihlášení',
    didntReceiveEmail: 'Nedostali jste email?',
    rememberPassword: 'Vzpomněli jste si na heslo?',
    tryAgain: 'Zkusit znovu',
  },
  profile: {
    title: 'Profil',
    about: 'O mně',
    activity: 'Aktivita',
    projects: 'Projekty',
    papers: 'Články',
    analyses: 'Analýzy',
    recentProjects: 'Nedávné projekty',
    recentAnalyses: 'Nedávné analýzy',
    accountDetails: 'Detaily účtu',
    accountType: 'Typ účtu',
    joinDate: 'Datum registrace',
    lastActive: 'Naposledy aktivní',
    projectsCreated: 'Vytvořené projekty',
    imagesUploaded: 'Nahrané obrázky',
    segmentationsCompleted: 'Dokončené segmentace',
    editProfile: 'Upravit profil',
    joined: 'Připojen',
    copyApiKey: 'Kopírovat API klíč',
    collaborators: 'Spolupracovníci',
    noCollaborators: 'Žádní spolupracovníci',
    connectedAccounts: 'Propojené účty',
    connect: 'Propojit',
    recentActivity: 'Nedávná aktivita',
    noRecentActivity: 'Žádná nedávná aktivita',
    statistics: 'Statistiky',
    totalImagesProcessed: 'Celkem zpracovaných obrázků',
    averageProcessingTime: 'Průměrná doba zpracování',
    fromLastMonth: 'od minulého měsíce',
    storageUsed: 'Využité úložiště',
    of: 'z',
    apiRequests: 'API požadavky',
    thisMonth: 'tento měsíc',
    recentPublications: 'Nedávné publikace',
    viewAll: 'Zobrazit vše',
    noPublications: 'Zatím žádné publikace',
    today: 'dnes',
    yesterday: 'včera',
    daysAgo: 'dní nazpět',
    completionRate: 'míra dokončení',
    createdProject: 'Vytvořil projekt',
    completedSegmentation: 'Dokončil segmentaci pro',
    uploadedImage: 'Nahrál obrázek',
    avatar: {
      uploadButton: 'Nahrát Avatar',
      selectFile: 'Vybrat obrázek avatara',
      cropTitle: 'Oříznutí Avatara',
      cropDescription: 'Ořízněte svůj avatar pro perfektní zobrazení',
      zoomLevel: 'Úroveň Přiblížení',
      cropInstructions:
        'Táhněte pro přesunutí, použijte posuvník pro přiblížení',
      applyChanges: 'Použít Změny',
      processing: 'Zpracovává se...',
      invalidFileType: 'Neplatný typ souboru. Vyberte prosím obrázek.',
      fileTooLarge: 'Soubor je příliš velký. Maximální velikost je 5MB.',
      cropError: 'Chyba při zpracování obrázku. Zkuste to znovu.',
      uploadSuccess: 'Avatar byl úspěšně nahrán',
      uploadError: 'Nepodařilo se nahrát avatar. Zkuste to znovu.',
    },
  },
  status: {
    segmented: 'Segmentováno',
    processing: 'Zpracovává se',
    queued: 'Ve frontě',
    failed: 'Chyba',
    no_segmentation: 'Bez segmentace',
    disconnected: 'Odpojeno od serveru',
    error: 'Chyba ML služby',
    ready: 'Připraven k segmentaci',
    online: 'Online',
    offline: 'Offline',
    noPolygons: 'Žádné polygony',
  },
  queue: {
    title: 'Segmentační fronta',
    connected: 'Připojeno',
    disconnected: 'Odpojeno',
    waiting: 'čeká',
    processing: 'zpracovává se',
    segmentAll: 'Segmentovat vše',
    segmentAllWithCount: 'Segmentovat vše ({{count}})',
    resegmentSelected: 'Znovu segmentovat vybrané ({{count}})',
    segmentMixed:
      'Segmentovat {{new}} + Znovu {{resegment}} (celkem {{total}})',
    segmentTooltip:
      '{{new}} nových obrázků bude segmentováno, {{resegment}} vybraných obrázků bude znovu segmentováno',
    totalProgress: 'Celkový postup',
    images: 'obrázků',
    loadingStats: 'Načítání statistik...',
    connectingMessage:
      'Připojuji se k serveru... Real-time aktualizace budou brzy dostupné.',
    emptyMessage:
      'Ve frontě nejsou žádné obrázky. Nahrajte obrázky a přidejte je do fronty pro segmentaci.',
    addingToQueue: 'Přidáváno do fronty...',
  },
  toast: {
    error: 'Došlo k chybě',
    success: 'Operace úspěšná',
    info: 'Informace',
    warning: 'Varování',
    loading: 'Načítání...',
    failedToUpdate: 'Nepodařilo se aktualizovat data. Zkuste to prosím znovu.',
    fillAllFields: 'Vyplňte prosím všechna pole',
    operationFailed: 'Operace selhala. Zkuste to prosím znovu.',
    unexpectedError: 'Neočekávaná chyba',
    somethingWentWrong: 'Něco se pokazilo. Zkuste to prosím později.',
    somethingWentWrongPage: 'Něco se pokazilo při načítání této stránky.',
    returnToHome: 'Návrat domů',
    operationCompleted: 'Operace byla úspěšně dokončena',
    dataSaved: 'Data byla úspěšně uložena',
    dataUpdated: 'Data byla úspěšně aktualizována',
    reconnecting: 'Znovu se připojuji k serveru...',
    reconnected: 'Připojení k serveru obnoveno',
    connectionFailed: 'Nepodařilo se obnovit připojení k serveru',
    segmentationRequested: 'Požadavek na segmentaci odeslán',
    segmentationCompleted: 'Segmentace obrázku dokončena',
    segmentationFailed: 'Segmentace selhala',
    segmentationResultFailed: 'Nepodařilo se získat výsledek segmentace',
    segmentationStatusFailed: 'Nepodařilo se zkontrolovat stav segmentace',
    exportCompleted: 'Export byl úspěšně dokončen!',
    exportFailed: 'Export selhal. Zkuste to prosím znovu.',
    project: {
      created: 'Projekt byl úspěšně vytvořen',
      createFailed: 'Nepodařilo se vytvořit projekt',
      deleted: 'Projekt byl úspěšně smazán',
      deleteFailed: 'Nepodařilo se smazat projekt',
      notFound: 'Projekt nebyl nalezen',
      urlCopied: 'URL projektu bylo zkopírováno do schránky',
      unshared: 'Projekt byl odebrán ze sdílených',
      invalidResponse: 'Odpověď serveru byla neplatná',
      readyForImages: 'je připraven pro obrázky',
      selected: '{{count}} obrázek vybrán',
      selected_other: '{{count}} obrázky vybrány',
      deleteSelected: 'Smazat vybrané',
    },
    profile: {
      loadFailed: 'Nepodařilo se načíst data profilu',
      consentUpdated: 'Předvolby souhlasu byly úspěšně aktualizovány',
    },
    segmentation: {
      deleted: 'Polygon byl smazán',
      cannotDeleteVertex:
        'Nelze smazat vrchol - polygon potřebuje alespoň 3 body',
      vertexDeleted: 'Vrchol byl úspěšně smazán',
      failed: 'Segmentace selhala',
      saved: 'Segmentace byla úspěšně uložena',
      started: 'Segmentace byla zahájena',
      completed: 'Segmentace byla úspěšně dokončena',
      completedWithCount: 'Segmentace dokončena! Nalezeno {{count}} objektů',
      noPolygons: 'Nebyly nalezeny žádné polygony segmentace',
      reloadFailed:
        'Nepodařilo se načíst výsledky segmentace. Obnovte prosím stránku.',
      autosaveFailed: 'Automatické ukládání selhalo - změny mohou být ztraceny',
    },
    upload: {
      failed: 'Nepodařilo se obnovit obrázky po nahrání',
    },
  },
  imageDeleted: 'Obrázek byl úspěšně smazán',
  deleteImageFailed: 'Smazání obrázku selhalo',
  deleteImageError: 'Chyba při mazání obrázku',
  imageAlreadyProcessing: 'Obrázek se již zpracovává',
  processImageFailed: 'Zpracování obrázku selhalo',
  exportDialog: {
    title: 'Možnosti exportu',
    includeMetadata: 'Zahrnout metadata',
    includeSegmentation: 'Zahrnout segmentaci',
    includeObjectMetrics: 'Zahrnout metriky objektů',
    exportMetricsOnly: 'Exportovat pouze metriky (XLSX)',
    selectImages: 'Vyberte obrázky k exportu',
    selectAll: 'Vybrat vše',
    selectNone: 'Odznačit vše',
    noImagesAvailable: 'Žádné obrázky nejsou k dispozici',
  },
  project: {
    selected: '{{count}} obrázek vybrán',
    selected_other: '{{count}} obrázky vybrány',
    deleteSelected: 'Smazat vybrané',
  },
  export: {
    advancedExport: 'Pokročilý export',
    advancedOptions: 'Pokročilé možnosti exportu',
    configureSettings:
      'Nakonfigurujte nastavení exportu pro vytvoření komplexního balíčku dat',
    general: 'Obecné',
    visualization: 'Vizualizace',
    formatsTab: 'Formáty',
    exportContents: 'Obsah exportu',
    selectContent: 'Vyberte typy obsahu k zahrnutí do exportu',
    includeOriginal: 'Zahrnout původní obrázky',
    includeVisualizations: 'Zahrnout vizualizace s očíslovanými polygony',
    includeDocumentation: 'Zahrnout dokumentaci a metadata',
    selectedImages: 'Vybrané obrázky',
    imagesSelected: '{{count}} z {{total}} obrázků vybráno',
    selectAll: 'Vybrat vše',
    selectNone: 'Nevybrat žádný',
    imageSelection: 'Výběr obrázků',
    chooseImages: 'Vyberte obrázky k zahrnutí do exportu',
    searchImages: 'Hledat obrázky...',
    sortBy: 'Seřadit podle',
    sortOptions: {
      date: 'Datum',
      name: 'Název',
      status: 'Stav',
    },
    showingImages: 'Zobrazeno {{start}}-{{end}} z {{total}}',
    noImagesFound: 'Žádné obrázky nenalezeny',
    qualitySettings: 'Nastavení kvality',
    imageQuality: 'Kvalita obrázku',
    compressionLevel: 'Úroveň komprese',
    outputResolution: 'Výstupní rozlišení',
    colorSettings: 'Nastavení barev',
    backgroundColor: 'Barva pozadí',
    strokeColor: 'Barva obrysu',
    strokeWidth: 'Šířka obrysu',
    fontSize: 'Velikost písma',
    showNumbers: 'Zobrazit čísla polygonů',
    showLabels: 'Zobrazit popisky',
    scaleConversion: 'Převod měřítka',
    pixelToMicrometerScale: 'Kalibrace pixel na mikrometry',
    scaleDescription:
      'Určete, kolik mikrometrů představuje jeden pixel pro převod měření',
    scalePlaceholder: 'např. 0,5 (1 pixel = 0,5 µm)',
    scaleUnit: 'µm/pixel',
    scaleWarning:
      'Poznámka: Hodnota měřítka nad 1 µm/pixel indikuje velmi nízkou magnifikaci. Prosím ověřte.',
    outputSettings: 'Nastavení výstupu',
    exportFormats: {
      yolo: 'YOLO Formát',
      excel: 'Excel Formát',
      json: 'JSON Formát',
    },
    exportFormatsLabel: 'Formáty exportu',
    exportToZip: 'Exportovat do ZIP archívu',
    generateExcel: 'Generovat Excel metriky',
    includeCocoFormat: 'Zahrnout anotace ve formátu COCO',
    includeJsonMetadata: 'Zahrnout JSON metadata',
    preparing: 'Příprava exportu...',
    processing: 'Zpracování {{current}} z {{total}}',
    packaging: 'Vytváření balíčku...',
    completed: 'Export dokončen',
    downloading: 'Stahování...',
    cancelled: 'Export zrušen',
    connected: 'Připojeno',
    disconnected: 'Odpojeno',
    reconnecting: 'Připojování...',
    startExport: 'Spustit export',
    cancel: 'Zrušit',
    download: 'Stáhnout',
    retry: 'Opakovat',
    close: 'Zavřít',
    exportError: 'Export selhal',
    exportFailed: 'Export selhal',
    exportComplete: 'Export dokončen',
    metricsExportComplete: 'Export metrik dokončen',
    connectionError: 'Spojení ztraceno během exportu',
    serverError: 'Nastala chyba serveru',
    invalidSelection: 'Vyberte prosím alespoň jeden obrázek',
    noData: 'Žádná data k exportu nejsou k dispozici',
    segmentationData: 'Data segmentace',
    spheroidMetrics: 'Metriky sféroidů',
    cocoFormat: 'Formát COCO',
    cocoFormatTitle: 'Export formátu COCO',
    downloadJson: 'Stáhnout JSON',
  },
  docs: {
    badge: 'Dokumentace',
    title: 'Dokumentace SpheroSeg',
    subtitle:
      'Komplexní průvodce používáním naší platformy pro segmentaci sféroidů',
    backTo: 'Zpět na {{page}}',
    navigation: 'Navigace',
    nav: {
      introduction: 'Úvod',
      gettingStarted: 'Začínáme',
      uploadingImages: 'Nahrávání obrázků',
      modelSelection: 'Výběr modelu',
      segmentationProcess: 'Proces segmentace',
      segmentationEditor: 'Editor segmentace',
      exportFeatures: 'Funkce exportu',
    },
    introduction: {
      title: 'Úvod',
      whatIs: 'Co je SpheroSeg?',
      description:
        'SpheroSeg je pokročilá platforma navržená speciálně pro segmentaci a analýzu buněčných sféroidů v mikroskopických snímcích. Náš nástroj kombinuje špičkové AI algoritmy s intuitivním rozhraním a poskytuje výzkumníkům přesnou detekci a analýzu hranic sféroidů.',
      developedBy:
        'Tato platforma byla vyvinuta Bc. Michalem Průškem, studentem Fakulty jaderné a fyzikálně inženýrské ČVUT v Praze, pod vedením Ing. Adama Novozámského, Ph.D. Projekt je spolupracující s výzkumníky z Ústavu biochemie a mikrobiologie UCT Praha.',
      addresses:
        'SpheroSeg řeší náročný úkol přesné identifikace a segmentace hranic sféroidů v mikroskopických snímcích, což je kritický krok v mnoha biomedicínských výzkumných pracovních postupech zahrnujících 3D kultury buněk.',
    },
    gettingStarted: {
      title: 'Začínáme',
      accountCreation: 'Vytvoření účtu',
      accountDescription:
        'K používání SpheroSeg budete potřebovat vytvořit účet. To nám umožní bezpečně ukládat vaše projekty a obrázky.',
      accountSteps: {
        step1: 'Přejděte na stránku registrace',
        step2:
          'Zadejte svou institucionální e-mailovou adresu a vytvořte heslo',
        step3: 'Vyplňte svůj profil jménem a institucí',
        step4:
          'Ověřte svou e-mailovou adresu prostřednictvím odkazu zaslaného do vaší schránky',
      },
      firstProject: 'Vytvoření vašeho prvního projektu',
      projectDescription:
        'Projekty vám pomáhají organizovat vaši práci. Každý projekt může obsahovat více obrázků a jejich odpovídající výsledky segmentace.',
      projectSteps: {
        step1: 'Z vašeho přehledu klikněte na "Nový projekt"',
        step2: 'Zadejte název a popis projektu',
        step3: 'Vyberte typ projektu (výchozí: Analýza sféroidů)',
        step4: 'Klikněte na "Vytvořit projekt" pro pokračování',
      },
    },
    uploadImages: {
      title: 'Nahrávání obrázků',
      description:
        'SpheroSeg podporuje různé formáty obrázků běžně používané v mikroskopii, včetně TIFF, PNG a JPEG.',
      methods: 'Metody nahrávání',
      methodsDescription: 'Existuje několik způsobů, jak nahrát vaše obrázky:',
      methodsList: {
        dragDrop: 'Přetáhněte soubory přímo do oblasti nahrávání',
        browse:
          'Klikněte na oblast nahrávání pro procházení a výběr souborů z vašeho počítače',
        batch: 'Hromadné nahrání více obrázků najednou',
      },
      note: 'Poznámka:',
      noteText:
        'Pro optimální výsledky zajistěte, aby vaše mikroskopické snímky měly dobrý kontrast mezi sféroidem a pozadím.',
    },
    modelSelection: {
      title: 'Výběr modelu',
      description:
        'SpheroSeg nabízí tři různé AI modely optimalizované pro různé případy použití. Vyberte model, který nejlépe vyhovuje vašim požadavkům na rychlost versus přesnost.',
      models: {
        hrnet: {
          name: 'HRNet (Malý)',
          inferenceTime: 'Doba inference: ~0,18 sekundy (GPU akcelerace)',
          bestFor: 'Nejlepší pro: Zpracování v reálném čase a rychlé výsledky',
          description:
            'Rychlý a efektivní model ideální pro rychlou segmentaci, když je rychlost upřednostňována před maximální přesností.',
        },
        cbam: {
          name: 'CBAM-ResUNet (Střední)',
          inferenceTime: 'Doba inference: ~0,20 sekundy (GPU akcelerace)',
          bestFor: 'Nejlepší pro: Přesná segmentace s mechanismy pozornosti',
          description:
            'Přesný segmentační model s mechanismy pozornosti pro přesnou detekci hranic sféroidů.',
        },
        unet_spherohq: {
          name: 'UNet (SpheroHQ)',
          inferenceTime: 'E2E čas: ~286ms na obrázek (ML inference: 181ms)',
          bestFor:
            'Nejlepší pro: Nejrychlejší zpracování, výborný pro real-time aplikace',
          description:
            'Nejrychlejší model po optimalizacích! Ideální pro zpracování velkého objemu dat v reálném čase.',
        },
      },
      howToSelect: 'Jak vybrat model',
      selectionSteps: {
        step1: 'Otevřete svůj projekt a přejděte na jakýkoli obrázek',
        step2:
          'V nástrojové liště projektu najděte rozbalovací nabídku výběru modelu',
        step3: 'Vyberte z HRNet, CBAM-ResUNet nebo UNet (SpheroHQ)',
        step4:
          'Upravte práh spolehlivosti (0,0-1,0) pro jemné doladění citlivosti detekce',
        step5: 'Váš výběr se automaticky uloží pro budoucí zpracování',
      },
      tip: 'Tip:',
      tipText:
        'Použijte UNet pro nejrychlejší zpracování s propustností 5,5 obrázků/sekundu. Vyberte CBAM-ResUNet pro maximální přesnost při výzkumné práci. Zvolte HRNet pro vyvážený výkon mezi rychlostí a kvalitou.',
    },
    segmentationProcess: {
      title: 'Proces segmentace',
      description:
        'Proces segmentace používá pokročilé AI modely k automatické detekci hranic sféroidů ve vašich mikroskopických snímcích. Systém podporuje jak automatické zpracování, tak manuální vylepšení.',
      queueBased: 'Zpracování založené na frontě',
      queueDescription:
        'SpheroSeg používá systém zpracovací fronty pro efektivní zpracování více úloh segmentace:',
      queueFeatures: {
        realTime:
          'Stav v reálném čase: WebSocket oznámení poskytují živé aktualizace o průběhu zpracování',
        batch: 'Hromadné zpracování: Zpracování více obrázků současně',
        priority:
          'Zpracování priority: Novější požadavky jsou zpracovány jako první',
        recovery:
          'Obnova po chybě: Neúspěšné úlohy jsou automaticky opakovány s podrobným hlášením chyb',
      },
      workflow: 'Pracovní postup automatické segmentace',
      workflowSteps: {
        step1: 'Nahrajte své mikroskopické snímky do projektu',
        step2: 'Vyberte svůj preferovaný AI model (HRNet nebo CBAM-ResUNet)',
        step3: 'V případě potřeby upravte práh spolehlivosti (výchozí: 0,5)',
        step4:
          'Klikněte na "Auto-segmentace" nebo použijte hromadné zpracování pro více obrázků',
        step5:
          'Sledujte průběh v reálném čase prostřednictvím stavových indikátorů',
        step6:
          'Zkontrolujte výsledky v editoru segmentace po dokončení zpracování',
      },
      polygonTypes: 'Typy polygonů',
      polygonDescription: 'Systém detekuje dva typy polygonů:',
      polygonTypesList: {
        external:
          'Externí polygony: Hlavní hranice sféroidů (zobrazeny ve výchozím stavu zeleně)',
        internal:
          'Interní polygony: Díry nebo vnitřní struktury v sféroidech (zobrazeny ve výchozím stavu červeně)',
      },
      processingNote: 'Doby zpracování se liší podle modelu:',
      processingTimes:
        'HRNet (~3s), CBAM-ResUNet (~7s). Vyberte podle vašich požadavků na přesnost a časová omezení.',
    },
    segmentationEditor: {
      title: 'Editor segmentace',
      description:
        'Editor segmentace je mocný nástroj pro vylepšování AI-generovaných segmentací a vytváření manuálních anotací. Obsahuje více režimů úprav, klávesové zkratky a pokročilé nástroje pro manipulaci s polygony.',
      editingModes: 'Režimy úprav',
      modes: {
        view: {
          title: 'Režim zobrazení',
          description:
            'Navigujte a prohlížejte polygony bez provádění změn. Klikněte na polygony pro jejich výběr a zobrazení detailů.',
        },
        editVertices: {
          title: 'Úprava vrcholů',
          description:
            'Přetáhněte jednotlivé vrcholy pro vylepšení hranic polygonů. Přesná kontrola pro úpravy hranic.',
        },
        addPoints: {
          title: 'Přidání bodů',
          description:
            'Vložte nové vrcholy mezi existující. Shift+klik pro automatické umístění bodu.',
        },
        createPolygon: {
          title: 'Vytvoření polygonu',
          description:
            'Nakreslete nové polygony od začátku. Klikněte pro přidání bodů, dvojklik pro dokončení.',
        },
        sliceMode: {
          title: 'Režim řezání',
          description: 'Řežte polygony na více částí kreslením čar skrz ně.',
        },
        deletePolygon: {
          title: 'Smazání polygonu',
          description:
            'Odstraňte nechtěné polygony kliknutím na ně. Užitečné pro eliminaci falešných detekcí.',
        },
      },
      keyFeatures: 'Klíčové funkce',
      features: {
        undoRedo:
          'Systém zpět/vpřed: Kompletní sledování historie s podporou Ctrl+Z/Ctrl+Y',
        autoSave:
          'Automatické ukládání: Periodické ukládání s vizuálními indikátory zobrazujícími neuložené změny',
        zoomPan:
          'Přiblížení a posouvání: Přiblížení kolečkem myši a navigace tažením',
        polygonManagement:
          'Správa polygonů: Zobrazit/skrýt, přejmenovat a hromadné operace',
        keyboardShortcuts:
          'Klávesové zkratky: Komplexní klávesové zkratky pro efektivní úpravy',
        realTimeFeedback:
          'Zpětná vazba v reálném čase: Živý náhled úprav a aktualizace stavu',
      },
      shortcuts: 'Základní klávesové zkratky',
      shortcutCategories: {
        navigation: 'Navigace:',
        actions: 'Akce:',
      },
      shortcutsList: {
        v: 'Režim zobrazení',
        e: 'Úprava vrcholů',
        a: 'Přidání bodů',
        n: 'Vytvoření polygonu',
        ctrlZ: 'Zpět',
        ctrlY: 'Vpřed',
        ctrlS: 'Uložit',
        delete: 'Odstranit vybrané',
      },
      workingWithPolygons: 'Práce s polygony',
      polygonSteps: {
        step1: 'Vyberte polygon kliknutím na něj (zvýrazněn modře při výběru)',
        step2: 'Přepněte na příslušný režim úprav pro váš úkol',
        step3: 'Proveďte své úpravy pomocí interakcí myši',
        step4:
          'Použijte panel polygonů napravo pro správu viditelnosti a vlastností',
        step5:
          'Uložte své změny periodicky nebo spoléhejte na automatické ukládání',
      },
      segmenting: 'Segmentování obrázku...',
      waitingInQueue: 'Čekání ve frontě',
      reloadingSegmentation: 'Obnovování dat segmentace...',
    },
    exportFeatures: {
      title: 'Funkce exportu',
      description:
        'SpheroSeg poskytuje komplexní možnosti exportu pro integraci s vaším výzkumným pracovním postupem. Exportujte data segmentace v několika formátech vhodných pro frameworky strojového učení a analytické nástroje.',
      packageContents: 'Obsah exportního balíčku',
      contents: {
        originalImages: {
          title: 'Původní obrázky',
          description:
            'Vysoce kvalitní původní mikroskopické snímky v jejich nativním formátu.',
        },
        visualizations: {
          title: 'Vizualizace',
          description:
            'Anotované obrázky s číslovanými polygony a přizpůsobitelnými barvami.',
        },
      },
      annotationFormats: 'Formáty anotací',
      formats: {
        coco: 'Formát COCO: Common Objects in Context - standardní formát pro frameworky detekce objektů jako PyTorch a TensorFlow',
        yolo: 'Formát YOLO: You Only Look Once - optimalizovaný formát pro modely detekce založené na YOLO',
        json: 'Vlastní JSON: Strukturovaný JSON formát s podrobnými souřadnicemi polygonů a metadaty',
      },
      calculatedMetrics: 'Vypočítané metriky',
      metricsDescription:
        'SpheroSeg automaticky počítá komplexní morfologické metriky pro každý detekovaný sféroid:',
      metricsCategories: {
        basic: {
          title: 'Základní měření:',
          items: {
            area: 'Plocha (pixely a škálované jednotky)',
            perimeter: 'Obvod',
            diameter: 'Ekvivalentní průměr',
            circularity: 'Kruhovitost',
          },
        },
        advanced: {
          title: 'Pokročilé metriky:',
          items: {
            feret: 'Feretovy průměry (max, min, poměr stran)',
            majorMinor: 'Hlavní/vedlejší průměr skrz těžiště',
            compactness: 'Kompaktnost, konvexnost, solidita',
            sphericity: 'Index sféricity',
          },
        },
      },
      exportFormats: 'Formáty exportu metrik',
      exportFormatsList: {
        excel:
          'Excel (.xlsx): Formátovaná tabulka se samostatnými listy pro souhrn a podrobná data',
        csv: 'CSV: Hodnoty oddělené čárkami pro snadný import do statistického softwaru',
        jsonExport: 'JSON: Strukturovaný datový formát pro programovou analýzu',
      },
      visualizationCustomization: 'Přizpůsobení vizualizace',
      customizationOptions: {
        colors:
          'Barvy polygonů: Přizpůsobte externí (zelené) a interní (červené) barvy polygonů',
        numbering: 'Číslování: Zobrazit/skrýt čísla polygonů pro identifikaci',
        strokeWidth: 'Šířka čáry: Upravte tloušťku čáry (1-10px)',
        fontSize:
          'Velikost písma: Kontrolujte velikost textu pro čísla polygonů (10-30px)',
        transparency:
          'Průhlednost: Nastavte průhlednost výplně polygonů (0-100%)',
      },
      howToExport: 'Jak exportovat',
      exportSteps: {
        step1: 'Přejděte na přehled vašeho projektu',
        step2:
          'Vyberte obrázky, které chcete exportovat (nebo exportujte všechny)',
        step3: 'Klikněte na "Pokročilý export" pro otevření dialogu exportu',
        step4:
          'Nakonfigurujte nastavení exportu ve třech záložkách: Obecné, Vizualizace a Formáty',
        step5: 'Zkontrolujte souhrn exportu',
        step6:
          'Klikněte na "Spustit export" pro vygenerování a stažení vašeho balíčku',
      },
      exportNote: 'Exportní balíčky jsou komplexní:',
      exportNoteText:
        'Každý export obsahuje dokumentaci, metadata a všechny vybrané typy obsahu organizované v přehledné struktuře složek pro snadné použití.',
    },
    sharedProjectsSection: {
      title: 'Sdílené projekty',
      description:
        'SpheroSeg umožňuje spolupráci na projektech prostřednictvím výkonných funkcí sdílení. Sdílejte své projekty s kolegy, spolupracujte na anotacích a společně analyzujte výsledky.',
      keyFeatures: 'Klíčové funkce',
      features: {
        projectSharing: {
          title: 'Sdílení projektů',
          description:
            'Sdílejte projekty s konkrétními uživateli prostřednictvím e-mailových pozvánek nebo generováním odkazů pro sdílení.',
        },
        permissions: {
          title: 'Správa oprávnění',
          description:
            'Nastavte oprávnění pouze pro čtení nebo úplný přístup pro sdílené uživatele.',
        },
        realTimeSync: {
          title: 'Synchronizace v reálném čase',
          description:
            'Změny provedené sdílenými uživateli jsou okamžitě viditelné pro všechny spolupracovníky.',
        },
        activityTracking: {
          title: 'Sledování aktivity',
          description:
            'Sledujte, kdo provedl jaké změny s podrobnými protokoly aktivit.',
        },
      },
      howToShare: 'Jak sdílet projekt',
      shareSteps: {
        step1: 'Otevřete projekt, který chcete sdílet',
        step2: 'Klikněte na tlačítko "Sdílet" v nástrojové liště projektu',
        step3: 'Zvolte mezi e-mailovou pozvánkou nebo odkazem pro sdílení',
        step4: 'Nastavte úroveň oprávnění (čtení nebo úplný přístup)',
        step5: 'Odešlete pozvánku nebo zkopírujte odkaz pro sdílení',
        step6: 'Spolupracovníci mohou přistupovat k projektu po přijetí',
      },
      accessingShared: 'Přístup ke sdíleným projektům',
      accessDescription:
        'Projekty sdílené s vámi se objeví v sekci "Sdílené projekty" na vašem řídicím panelu. Můžete je zobrazit, upravovat (pokud máte oprávnění) a analyzovat stejně jako své vlastní projekty.',
      collaborationTip: 'Tip pro spolupráci:',
      collaborationTipText:
        'Používejte funkci komentářů k zanechávání poznámek na konkrétních obrázcích nebo segmentacích pro efektivní komunikaci s vašim týmem.',
    },
    footer: {
      backToHome: 'Zpět domů',
      backToTop: 'Zpět nahoru',
    },
  },
  legal: {
    terms: {
      title: 'Podmínky použití',
      lastUpdated: 'Naposledy aktualizováno: leden 2025',
      disclaimer:
        'Používáním SpheroSeg souhlasíte s těmito podmínkami. Pečlivě si je prosím přečtěte.',
      sections: {
        acceptance: {
          title: '1. Přijetí podmínek',
          content:
            'Přístupem nebo používáním SpheroSeg ("Služba") souhlasíte, že budete vázáni těmito Podmínkami použití ("Podmínky") a všemi platnými zákony a předpisy. Pokud nesouhlasíte s některou z těchto podmínek, je vám zakázáno používat tuto službu. Tyto Podmínky představují právně závaznou smlouvu mezi vámi a SpheroSeg.',
        },
        useLicense: {
          title: '2. Licence k používání a povolené použití',
          content: 'Povolení k používání SpheroSeg je uděleno pro:',
          permittedUses: [
            'Osobní, nekomerční výzkumné účely',
            'Akademický a vzdělávací výzkum',
            'Vědecké publikace a studie',
            'Biomedicínský výzkum a analýzu',
          ],
          licenseNote:
            'Jedná se o udělení licence, nikoli převod vlastnictví. Službu nesmíte používat pro komerční účely bez výslovného písemného souhlasu.',
        },
        dataUsage: {
          title: '3. Používání dat a strojové učení',
          importantTitle: 'Důležité: Použití vašich dat',
          importantContent:
            'Nahráváním obrázků a dat do SpheroSeg souhlasíte s tím, že tato data použijeme ke zlepšení a trénování našich modelů strojového učení pro lepší přesnost segmentace.',
          ownershipTitle: 'Vlastnictví dat:',
          ownershipContent:
            'Zachováváte si vlastnictví všech dat, která do SpheroSeg nahrajete. Nicméně používáním naší služby nám udělujete povolení k:',
          permissions: [
            'Zpracování vašich obrázků pro analýzu segmentace',
            'Používání nahraných dat (v anonymizované formě) ke zlepšení našich ML algoritmů',
            'Zvyšování přesnosti modelů prostřednictvím kontinuálního učení',
            'Vývoji nových funkcí a schopností segmentace',
          ],
          protectionNote:
            'Všechna data používaná pro trénování ML jsou anonymizována a zbavena identifikačních informací. Vaše surová data nesdílíme s třetími stranami bez výslovného souhlasu.',
        },
        userResponsibilities: {
          title: '4. Povinnosti uživatele',
          content: 'Souhlasíte s tím, že:',
          responsibilities: [
            'Budete službu používat pouze k zákonným účelům',
            'Budete respektovat práva duševního vlastnictví',
            'Nebudete se pokoušet o reverzní inženýrství nebo kompromitování služby',
            'Při vytváření účtu poskytnete přesné informace',
            'Budete udržovat zabezpečení vašich přihlašovacích údajů',
          ],
        },
        serviceAvailability: {
          title: '5. Dostupnost služby a omezení',
          content:
            'Ačkoli se snažíme udržovat kontinuální dostupnost služby, SpheroSeg je poskytována "tak jak je" bez jakýchkoli záruk. Nezaručujeme nepřerušený přístup a služba může podléhat údržbě, aktualizacím nebo dočasné nedostupnosti.',
        },
        limitationLiability: {
          title: '6. Omezení odpovědnosti',
          content:
            'SpheroSeg, její vývojáři nebo přidružené společnosti nenesou v žádném případě odpovědnost za jakékoli nepřímé, náhodné, zvláštní, následné nebo trestní škody, včetně, ale nejen, ztráty dat, zisků nebo obchodních příležitostí vyplývajících z vašeho používání služby.',
        },
        privacy: {
          title: '7. Ochrana soukromí a dat',
          content:
            'Vaše soukromí je pro nás důležité. Prosím, prostudujte si naše Zásady ochrany osobních údajů, které upravují způsob, jakým shromažďujeme, používáme a chráníme vaše osobní informace a výzkumná data.',
        },
        changes: {
          title: '8. Změny podmínek',
          content:
            'Vyhrazujeme si právo kdykoli tyto Podmínky upravit. Změny nabydou účinnosti okamžitě po zveřejnění. Vaše pokračující používání služby představuje přijetí upravených Podmínek.',
        },
        termination: {
          title: '9. Ukončení',
          content:
            'Kterákoli strana může tuto smlouvu kdykoli ukončit. Po ukončení okamžitě zanikne vaše právo na přístup ke službě, ačkoli tyto Podmínky zůstávají v platnosti ohledně předchozího použití.',
        },
        governingLaw: {
          title: '10. Rozhodné právo',
          content:
            'Tyto Podmínky se řídí a vykládají v souladu s platnými zákony. Všechny spory budou řešeny prostřednictvím závazného arbitráže nebo u příslušných soudů.',
        },
      },
      contact: {
        title: 'Kontaktní informace:',
        content:
          'Pokud máte otázky ohledně těchto Podmínek, kontaktujte ná<NAME_EMAIL>',
      },
      navigation: {
        backToHome: 'Zpět domů',
        privacyPolicy: 'Zásady ochrany osobních údajů',
      },
    },
    privacy: {
      title: 'Zásady ochrany osobních údajů',
      lastUpdated: 'Naposledy aktualizováno: leden 2025',
      disclaimer:
        'Vaše soukromí je pro nás důležité. Tyto zásady vysvětlují, jak shromažďujeme, používáme a chráníme vaše data.',
      sections: {
        introduction: {
          title: '1. Úvod',
          content:
            'Tyto Zásady ochrany osobních údajů vysvětlují, jak SpheroSeg ("my", "nás", "naše") shromažďuje, používá, chrání a sdílí vaše informace při používání naší platformy pro segmentaci a analýzu sféroidů. Používáním naší služby souhlasíte s praktikami týkajícími se dat popsanými v těchto zásadách.',
        },
        informationCollected: {
          title: '2. Informace, které shromažďujeme',
          content:
            'Shromažďujeme informace, které nám přímo poskytujete při vytváření účtu, nahrávání obrázků, vytváření projektů a interakci s našimi službami.',
          personalInfo: {
            title: '2.1 Osobní informace',
            items: [
              'Jméno a e-mailová adresa',
              'Přidružení k instituci nebo organizaci',
              'Přihlašovací údaje a preference účtu',
              'Kontaktní informace pro žádosti o podporu',
            ],
          },
          researchData: {
            title: '2.2 Výzkumná data a obrázky',
            ownershipTitle: 'Vaše výzkumná data',
            ownershipContent:
              'Zachováváte si plné vlastnictví všech obrázků a výzkumných dat, která do SpheroSeg nahrajete. Nikdy si nenárokujeme vlastnictví vašeho obsahu.',
            items: [
              'Obrázky, které nahrajete k analýze',
              'Metadata projektů a nastavení',
              'Výsledky segmentace a anotace',
              'Parametry analýzy a vlastní konfigurace',
            ],
          },
          usageInfo: {
            title: '2.3 Informace o používání',
            items: [
              'Protokolová data a časové značky přístupu',
              'Informace o zařízení a typu prohlížeče',
              'Vzory používání a interakce s funkcemi',
              'Metriky výkonu a hlášení chyb',
            ],
          },
        },
        mlTraining: {
          title: '3. Strojové učení a zlepšování dat',
          importantTitle: 'Důležité: Použití vašich dat pro trénování AI',
          importantIntro:
            'Pro kontinuální zlepšování našich algoritmů segmentace můžeme používat nahrané obrázky a data k trénování a vylepšování našich modelů strojového učení.',
          controlTitle: 'Máte plnou kontrolu nad svými daty:',
          controlContent:
            'Při vytváření účtu si můžete vybrat, zda povolíte použití vašich dat pro trénování ML. Tyto preference můžete kdykoli změnit.',
          manageTitle: 'Pro správu vašeho souhlasu:',
          manageContent:
            'Přejděte do Nastavení → záložka Soukromí ve vašem přehledu. Tam můžete povolit nebo zakázat souhlas s trénováním ML a vybrat konkrétní účely (zlepšení algoritmů, vývoj funkcí), pro které mohou být vaše data použita.',
          howWeUse: {
            title: 'Jak používáme vaše data pro ML:',
            items: [
              'Trénování modelu: Obrázky se používají k trénování algoritmů segmentace pro lepší přesnost',
              'Vylepšení algoritmů: Vaše opravy segmentace pomáhají zlepšit automatickou detekci',
              'Vývoj funkcí: Vzory používání vedou vývoj nových analytických nástrojů',
              'Zajištění kvality: Data pomáhají validovat a testovat nové verze modelů',
            ],
          },
          protection: {
            title: 'Ochrana dat při trénování ML:',
            items: [
              'Anonymizace: Všechna data jsou anonymizována před použitím při trénování ML',
              'Odstranění metadat: Osobní a institucionální identifikační informace jsou odstraněny',
              'Bezpečné zpracování: Trénování probíhá v bezpečných, izolovaných prostředích',
              'Žádná distribuce surových dat: Vaše původní obrázky nejsou nikdy sdíleny s třetími stranami',
            ],
          },
        },
        howWeUse: {
          title: '4. Jak používáme vaše informace',
          content: 'Shromážděné informace používáme k:',
          purposes: [
            'Poskytování a udržování služeb segmentace',
            'Zpracování vašich obrázků a generování výsledků analýzy',
            'Zlepšování našich algoritmů a vývoji nových funkcí',
            'Komunikaci s vámi ohledně vašeho účtu a aktualizací',
            'Poskytování technické podpory a řešení problémů',
            'Dodržování právních povinností a ochraně našich práv',
          ],
        },
        dataSecurity: {
          title: '5. Zabezpečení a ochrana dat',
          content: 'Implementujeme robustní bezpečnostní opatření včetně:',
          measures: [
            'Šifrování dat při přenosu a v klidu',
            'Pravidelné bezpečnostní audity a hodnocení zranitelností',
            'Kontroly přístupu a autentizační systémy',
            'Bezpečné zálohování a postupy obnovy po havárii',
            'Školení zaměstnanců v oblasti bezpečnosti a omezení přístupu',
          ],
        },
        dataSharing: {
          title: '6. Sdílení dat a třetí strany',
          noSaleStatement:
            'Neprodáváme vaše osobní informace ani výzkumná data.',
          sharingContent:
            'Informace můžeme sdílet pouze v těchto omezených případech:',
          circumstances: [
            'S vaším výslovným souhlasem',
            'Pro dodržení právních povinností nebo soudních příkazů',
            'S důvěryhodnými poskytovateli služeb, kteří pomáhají provozovat naši platformu (pod přísnými dohodami o mlčenlivosti)',
            'Pro ochranu našich práv, bezpečnosti nebo majetku',
            'V anonymizované, agregované formě pro výzkumné publikace (s vaším souhlasem)',
          ],
        },
        privacyRights: {
          title: '7. Vaše práva na soukromí a volby',
          content: 'Máte právo na:',
          rights: [
            'Přístup: Požádat o kopie vašich osobních dat a výzkumného obsahu',
            'Oprava: Aktualizovat nebo opravit nepřesné informace',
            'Vymazání: Požádat o vymazání vašeho účtu a souvisejících dat',
            'Přenositelnost: Exportovat vaše data ve strojově čitelném formátu',
            'Odhlášení: Požádat o vyloučení z trénování ML. Poznámka: To může omezit následující funkce: přesnost automatické segmentace, personalizovaná doporučení modelů, adaptivní návrhy prahu, optimalizace dávkového zpracování a budoucí vylepšení poháněná AI. Kontaktujte podporu pro konkrétní dopady na váš účet.',
            'Omezení: Omezit způsob, jakým zpracováváme vaše informace',
          ],
          contactNote:
            'Pro uplatnění těchto práv nás <NAME_EMAIL>. Odpovíme do 30 dnů.',
        },
        dataRetention: {
          title: '8. Uchovávání dat',
          content: 'Rozlišujeme mezi osobními daty a daty pro trénování ML:',
          categories: [
            'Osobní/účetní data: Všechny osobní identifikátory, informace o profilu, nastavení účtu a transakční historie budou trvale vymazány do 90 dnů od uzavření účtu.',
            'Výzkumná data: Původní obrázky a projektová data propojená s vaším účtem budou vymazána do 90 dnů od uzavření účtu.',
            'Data pro trénování ML: Data používaná pro trénování ML jsou nejprve anonymizována/pseudonymizována k odstranění všech osobních identifikátorů. Tato anonymizovaná data mohou být uchovávána neomezeně dlouho k zachování zlepšení modelu, pokud se specificky neodhlásíte z trénování ML nebo nepožádáte o úplné vymazání.',
            'Možnosti odhlášení: Můžete požádat o úplné vymazání všech dat, včetně anonymizovaných dat pro trénování ML, kontaktováním <EMAIL>. Doba zpracování je obvykle 30 dnů.',
          ],
        },
        internationalTransfers: {
          title: '9. Mezinárodní přenosy dat',
          content:
            'Vaše data mohou být zpracovávána v jiných zemích než ve vaší vlastní. Zajišťujeme odpovídající záruky a ochranu pro mezinárodní přenosy, včetně standardních smluvních doložek a rozhodnutí o přiměřenosti.',
        },
        childrensPrivacy: {
          title: '10. Soukromí dětí',
          content:
            'Naše služba je určena pro výzkumníky a není zaměřena na děti mladší 16 let. Vědomě neshromažďujeme osobní informace od dětí mladších 16 let. Pokud takové shromažďování objevíme, informace okamžitě vymažeme.',
        },
        policyChanges: {
          title: '11. Změny těchto zásad',
          content:
            'Můžeme aktualizovat tyto Zásady ochrany osobních údajů, aby odrážely změny v našich postupech nebo právních požadavcích. O podstatných změnách vás budeme informovat prostřednictvím e-mailu nebo výrazného oznámení na naší webové stránce. Pokračující používání představuje přijetí aktualizovaných podmínek.',
        },
        contact: {
          title: '12. Kontaktní informace',
          dpo: 'Pověřenec pro ochranu osobních údajů: <EMAIL>',
          general: 'Obecné dotazy: <EMAIL>',
          postal: 'Poštovní adresa:',
          address: {
            line1: 'ÚTIA AV ČR',
            line2: 'Pod Vodárenskou věží 4',
            line3: '182 08 Praha 8',
            line4: 'Česká republika',
          },
        },
      },
      navigation: {
        backToHome: 'Zpět domů',
        termsOfService: 'Podmínky použití',
      },
    },
  },
  websocket: {
    reconnecting: 'Znovu se připojuji k serveru...',
    reconnected: 'Připojení k serveru obnoveno',
    reconnectFailed: 'Nepodařilo se obnovit připojení k serveru',
    connectionLost: 'Spojení se serverem ztraceno',
    connected: 'Připojeno k aktualizacím v reálném čase',
    disconnected: 'Odpojeno od aktualizací v reálném čase',
  },
  contextMenu: {
    editPolygon: 'Upravit polygon',
    splitPolygon: 'Rozdělit polygon',
    deletePolygon: 'Smazat polygon',
    confirmDeletePolygon: 'Opravdu chcete smazat polygon?',
    deletePolygonDescription:
      'Tato akce je nevratná. Polygon bude trvale odstraněn ze segmentace.',
    duplicateVertex: 'Duplikovat bod',
    deleteVertex: 'Smazat bod',
  },
  metrics: {
    info: 'Metriky jsou vyhodnocovány pouze pro externí polygony. Plochy interních polygonů (děr) jsou automaticky odečteny od příslušných externích polygonů.',
    spheroid: 'Sféroid',
    area: 'Plocha',
    perimeter: 'Obvod',
    equivalentDiameter: 'Ekvivalentní průměr',
    circularity: 'Kruhovitost',
    feretMax: 'Feretův maximum',
    feretMin: 'Feretův minimální',
    compactness: 'Kompaktnost',
    convexity: 'Konvexita',
    solidity: 'Solidita',
    sphericity: 'Sféricita',
    feretAspectRatio: 'Feretův poměr stran',
    noPolygonsFound: 'Nebyly nalezeny žádné polygony pro analýzu',
  },
  keyboardShortcuts: {
    title: 'Klávesové zkratky',
    buttonLabel: 'Zkratky',
    viewMode: 'Režim prohlížení',
    editVertices: 'Režim úprav bodů',
    addPoints: 'Režim přidávání bodů',
    createPolygon: 'Vytvořit nový polygon',
    sliceMode: 'Režim řezání',
    deleteMode: 'Režim mazání',
    holdToAutoAdd: 'Držte pro automatické přidávání bodů',
    undo: 'Zpět',
    redo: 'Znovu',
    deleteSelected: 'Smazat vybraný polygon',
    cancelOperation: 'Zrušit aktuální operaci',
    zoomIn: 'Přiblížit',
    zoomOut: 'Oddálit',
    resetView: 'Resetovat zobrazení',
    helperText:
      'Tyto zkratky fungují v editoru segmentace pro rychlejší a pohodlnější práci.',
  },
  accessibility: {
    toggleSidebar: 'Přepnout boční panel',
    toggleMenu: 'Přepnout menu',
    selectLanguage: 'Vybrat jazyk',
    selectTheme: 'Vybrat téma',
    breadcrumb: 'drobečková navigace',
    pagination: 'stránkování',
    close: 'Zavřít',
    more: 'Více',
    goToPreviousPage: 'Jít na předchozí stránku',
    goToNextPage: 'Jít na další stránku',
    previousPage: 'Předchozí',
    nextPage: 'Další',
    morePages: 'Více stránek',
    previousSlide: 'Předchozí snímek',
    nextSlide: 'Další snímek',
    gridView: 'Mřížkové zobrazení',
    listView: 'Seznamové zobrazení',
  },
  footer: {
    appName: 'SpheroSeg',
    description:
      'Pokročilá platforma pro segmentaci a analýzu sféroidů pro biomedicínské výzkumníky, poskytující AI nástroje pro analýzu mikroskopických buněčných obrázků.',
    contact: 'Kontakt',
    institution: 'Instituce',
    institutionName: 'ÚTIA AV ČR',
    address: 'Adresa',
    addressText: 'Pod Vodárenskou věží 4, 182 08 Praha 8',
    resources: 'Zdroje',
    documentation: 'Dokumentace',
    features: 'Funkce',
    tutorials: 'Návody',
    research: 'Výzkum',
    legal: 'Právní',
    termsOfService: 'Podmínky služby',
    privacyPolicy: 'Zásady ochrany osobních údajů',
    contactUs: 'Kontaktujte nás',
    copyright:
      '© {{year}} SpheroSeg. Vyvinuto na ÚTIA AV ČR (Ústav teorie informace a automatizace, Akademie věd České republiky).',
  },
  sharing: {
    processingInvitation: 'Zpracování pozvánky...',
    share: 'Sdílet',
    shared: 'Sdíleno',
    shareProject: 'Sdílet projekt',
    shareDescription: 'Sdílejte projekt "{{title}}" s kolegy a spolupracovníky',
    shareByEmail: 'Sdílet emailem',
    shareByLink: 'Sdílet odkazem',
    emailAddress: 'Emailová adresa',
    enterEmailPlaceholder: 'Zadejte emailovou adresu',
    optionalMessage: 'Volitelná zpráva',
    messagePlaceholder: 'Přidejte osobní zprávu k pozvánce...',
    sendInvitation: 'Odeslat pozvánku',
    sending: 'Odesílání...',
    emailSent: 'Email pozvánka byla odeslána!',
    emailRequired: 'Emailová adresa je povinná',
    emailShareFailed: 'Nepodařilo se odeslat email pozvánku',
    linkExpiry: 'Platnost odkazu',
    neverExpires: 'Nikdy nevyprší',
    hours: 'hodin',
    days: 'dní',
    generateLink: 'Vygenerovat odkaz',
    generating: 'Generování...',
    linkGenerated: 'Odkaz pro sdílení byl vytvořen!',
    linkCopied: 'Odkaz zkopírován do schránky',
    linkCopyFailed: 'Nepodařilo se zkopírovat odkaz',
    linkShareFailed: 'Nepodařilo se vygenerovat odkaz',
    emailInvitations: 'Emailové pozvánky',
    shareLinks: 'Odkazy pro sdílení',
    shareRevoked: 'Sdílení bylo zrušeno',
    revokeShareFailed: 'Nepodařilo se zrušit sdílení',
    failedToLoadShares: 'Nepodařilo se načíst seznam sdílení',
    status: {
      pending: 'Čekající',
      accepted: 'Přijato',
      revoked: 'Zrušeno',
    },
    sharedWithYou: 'Sdíleno s vámi',
    sharedBy: 'Sdílel:',
    sharedProjects: 'Sdílené projekty',
    noSharedProjects: 'Žádné projekty s vámi nejsou sdíleny',
    removeFromShared: 'Odebrat ze sdílených',
    acceptInvitation: 'Přijmout pozvánku',
    invitationAccepted: 'Pozvánka byla přijata!',
    invitationExpired: 'Tato pozvánka již expirovala',
    invitationInvalid: 'Neplatná pozvánka',
    loginToAccept: 'Pro přijetí pozvánky se přihlaste',
    accepting: 'Přijímání',
    redirectingToProject: 'Přesměrování na projekt',
    invitedEmail: 'Pozvaný email',
    pendingInvitations: 'Čekající pozvánky',
    sentOn: 'Odesláno',
    joinedViaLink: 'Připojeni přes odkaz',
    joinedViaLinkOn: 'Připojeno',
    loadingShare: 'Načítání informací o sdílení...',
    projectSharedBy: 'Projekt sdílel',
    signInRequired: 'Vyžadováno přihlášení',
    signInToAccept: 'Pro přijetí pozvánky se prosím přihlaste',
    signInButton: 'Přihlásit se',
    goToProject: 'Přejít na projekt',
    backToHome: 'Zpět domů',
    acceptFailed: 'Nepodařilo se přijmout pozvánku',
    differentEmail: 'Tato pozvánka je pro jinou emailovou adresu',
    acceptedUsers: 'Přijatí uživatelé',
    activeShareLinks: 'Aktivní odkazy pro sdílení',
    joinedOn: 'Připojeno',
    resendInvitation: 'Odeslat pozvánku znovu',
    invitationResent: 'Pozvánka byla odeslána znovu',
    resendFailed: 'Nepodařilo se odeslat pozvánku znovu',
    revokeAccess: 'Zrušit přístup',
    cancelInvitation: 'Zrušit pozvánku',
    reminderMessage:
      'Toto je připomínka, že jste byli pozváni ke spolupráci na projektu',
  },
  error: 'Chyba',
  segmentationEditor: {
    reloadingSegmentation: 'Obnovování segmentace...',
    segmenting: 'Segmentování...',
    waitingInQueue: 'Čekání ve frontě...',
    error: {
      title: 'Chyba segmentace',
      description:
        'Při načítání segmentačních dat došlo k chybě. Může to být způsobeno problémy se sítí nebo serverem.',
      errorDetails: 'Podrobnosti chyby',
      tryAgain: 'Zkusit znovu',
      unsavedChanges: 'Neuložené změny',
    },
    export: {
      exportAllMetrics: 'Exportovat všechny metriky jako XLSX',
      exportUnavailable: 'Export není k dispozici',
      loading: 'Načítání...',
    },
  },
};
