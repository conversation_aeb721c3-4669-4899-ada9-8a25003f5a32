export default {
  common: {
    appName: 'Sphäroid-Segmentierung',
    loading: 'Laden...',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    cancel: 'Abbrechen',
    delete: '<PERSON><PERSON><PERSON>',
    edit: '<PERSON>bei<PERSON>',
    create: '<PERSON><PERSON><PERSON><PERSON>',
    search: '<PERSON><PERSON>',
    error: '<PERSON><PERSON>',
    success: '<PERSON>rfolg',
    back: '<PERSON><PERSON><PERSON>',
    signIn: 'Anmelden',
    signUp: 'Registrieren',
    signOut: 'Abmelden',
    settings: 'Einstellungen',
    profile: 'Profil',
    dashboard: 'Dashboard',
    project: 'Projekt',
    projects: 'Projekte',
    polygon: 'Polygon',
    newProject: 'Neues Projekt',
    upload: 'Hochladen',
    uploadImages: 'Bilder hochladen',
    recentAnalyses: 'Aktuelle Analysen',
    noProjects: 'Keine Projekte gefunden',
    noImages: 'Keine Bilder gefunden',
    createYourFirst: '<PERSON><PERSON>ellen Sie Ihr erstes Projekt, um zu beginnen',
    tryAgain: 'Erne<PERSON> versuchen',
    email: 'E-Mail',
    password: 'Passwort',
    name: 'Name',
    description: '<PERSON><PERSON>re<PERSON><PERSON>',
    date: 'Da<PERSON>',
    status: 'Status',
    images: 'Bilder',
    image: 'Bild',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    theme: 'Design',
    language: 'Sprache',
    light: 'Hell',
    dark: 'Dunkel',
    system: 'System',
    welcome: 'Willkommen bei der Sphäroid-Segmentierungsplattform',
    account: 'Konto',
    notifications: 'Benachrichtigungen',
    passwordConfirm: 'Passwort bestätigen',
    manageAccount: 'Konto verwalten',
    documentation: 'Dokumentation',
    changePassword: 'Passwort ändern',
    deleteAccount: 'Konto löschen',
    termsOfService: 'Nutzungsbedingungen',
    privacyPolicy: 'Datenschutzerklärung',
    createAccount: 'Konto erstellen',
    signInToAccount: 'Bei Ihrem Konto anmelden',
    sort: 'Sortieren',
    no_preview: 'Keine Vorschau',
    openMenu: 'Menü öffnen',
    logOut: 'Abmelden',
    pageNotFound: 'Ups! Seite nicht gefunden',
    returnToHome: 'Zurück zur Startseite',
    next: 'Weiter',
    copy: 'Kopieren',
    noImage: 'Kein Bild',
    untitledImage: 'Unbenanntes Bild',
    rename: 'Umbenennen',
    getStarted: 'Erste Schritte',
    learnMore: 'Mehr erfahren',
    close: 'Schließen',
    redirectingToDashboard: 'Weiterleitung zum Dashboard...',
  },
  landing: {
    hero: {
      badge: 'Fortschrittliche Sphäroid-Segmentierungsplattform',
      title: 'KI-gestützte Zellanalyse für biomedizinische Forschung',
      subtitle:
        'Verbessern Sie Ihre mikroskopische Zellbildanalyse mit unserer hochmodernen Sphäroid-Segmentierungsplattform. Entwickelt für Forscher, die Präzision und Effizienz suchen.',
      getStarted: 'Loslegen',
      learnMore: 'Mehr erfahren',
    },
    about: {
      badge: 'Unsere Mission',
      title: 'Biomedizinische Forschung durch Technologie vorantreiben',
      description1:
        'Unsere Plattform wurde von Bc. Michal Průšek, Student an der Fakultät für Kernwissenschaften und Physikalisches Ingenieurwesen (FJFI) an der Tschechischen Technischen Universität Prag, unter der Leitung von Ing. Adam Novozámský, Ph.D. entwickelt.',
      description2:
        'Dieses Projekt ist eine Zusammenarbeit mit Forschern vom Institut für Biochemie und Mikrobiologie an der UCT Prag (VŠCHT Praha).',
      description3:
        'Wir kombinieren modernste KI-Modelle mit einer intuitiven Benutzeroberfläche, um Forschern leistungsstarke Werkzeuge für die mikroskopische Bildanalyse zur Verfügung zu stellen, mit Fokus auf Sphäroid-Segmentierung mit unvergleichlicher Präzision.',
      contactText: 'Für Anfragen kontaktieren Sie uns bitte unter',
    },
    cta: {
      title: 'Bereit, Ihren Zellanalyse-Workflow zu transformieren?',
      subtitle:
        'Schließen Sie sich führenden Forschern an, die unsere Plattform bereits nutzen, um ihre Entdeckungen zu beschleunigen.',
      cardTitle: 'Heute beginnen',
      cardDescription:
        'Melden Sie sich für ein kostenloses Konto an und erleben Sie die Kraft der KI-gesteuerten Sphäroid-Segmentierung.',
      createAccount: 'Konto erstellen',
    },
    features: {
      badge: 'Leistungsstarke Funktionen',
      title: 'Fortgeschrittene Tools für die biomedizinische Forschung',
      subtitle:
        'Unsere Plattform bietet eine umfassende Suite von Funktionen, die darauf ausgelegt sind, Ihren Sphäroid-Segmentierungs-Workflow zu optimieren.',
      cards: {
        advancedSegmentation: {
          title: 'Erweiterte Segmentierung',
          description:
            'Präzise Sphäroid-Erkennung mit Grenzanalyse für genaue Zellmessungen.',
        },
        aiPowered: {
          title: 'KI-gestützte Analyse',
          description:
            'Nutzen Sie Deep-Learning-Algorithmen für automatische Zellerkennung und -klassifizierung.',
        },
        effortlessUploads: {
          title: 'Müheloses Hochladen',
          description:
            'Ziehen Sie Ihre mikroskopischen Bilder per Drag & Drop für sofortige Verarbeitung und Analyse.',
        },
        statisticalInsights: {
          title: 'Statistische Einblicke',
          description:
            'Umfassende Metriken und Visualisierungen zur Extraktion bedeutsamer Datenmuster.',
        },
        collaboration: {
          title: 'Kollaborationswerkzeuge',
          description:
            'Teilen Sie Projekte mit Kollegen und arbeiten Sie in Echtzeit an Forschungsergebnissen zusammen.',
        },
        processingPipeline: {
          title: 'Verarbeitungspipeline',
          description:
            'Automatisierter Workflow von der Vorverarbeitung bis zur finalen Analyse mit anpassbaren Parametern.',
        },
      },
    },
  },
  dashboard: {
    manageProjects: 'Verwalten Sie Ihre Forschungsprojekte und Analysen',
    projectGallery: 'Projektgalerie',
    projectGalleryDescription:
      'Durchsuchen und verwalten Sie alle Ihre Segmentierungsprojekte',
    statsOverview: 'Statistik-Übersicht',
    totalProjects: 'Projekte gesamt',
    activeProjects: 'Aktive Projekte',
    totalImages: 'Bilder gesamt',
    totalAnalyses: 'Analysen gesamt',
    lastUpdated: 'Zuletzt aktualisiert',
    noProjectsDescription:
      'Sie haben noch keine Projekte erstellt. Erstellen Sie Ihr erstes Projekt, um zu beginnen.',
    noImagesDescription: 'Laden Sie einige Bilder hoch, um zu beginnen',
    searchProjectsPlaceholder: 'Projekte suchen...',
    searchImagesPlaceholder: 'Bilder nach Namen suchen...',
    sortBy: 'Sortieren nach',
    name: 'Name',
    lastChange: 'Letzte Änderung',
    status: 'Status',
    stats: {
      totalProjects: 'Projekte gesamt',
      totalProjectsDesc: 'Aktive Sphäroid-Studien',
      processedImages: 'Verarbeitete Bilder',
      processedImagesDesc: 'Erfolgreich segmentiert',
      uploadedToday: 'Heute hochgeladen',
      uploadedTodayDesc: 'Sphäroid-Bilder',
      storageUsed: 'Genutzter Speicher',
      totalSpaceUsed: 'Gesamt genutzter Speicher',
    },
    completed: 'Abgeschlossen',
    processing: 'Verarbeitung',
    pending: 'Ausstehend',
    failed: 'Fehlgeschlagen',
    storageUsed: 'Genutzter Speicher',
  },
  projects: {
    createProject: 'Neues Projekt erstellen',
    createProjectDesc:
      'Fügen Sie ein neues Projekt hinzu, um Ihre Sphäroid-Bilder und Analysen zu organisieren.',
    projectNamePlaceholder: 'z.B. HeLa-Zell-Sphäroide',
    projectDescPlaceholder:
      'z.B. Analyse von Tumor-Sphäroiden für Arzneimittelresistenz-Studien',
    creatingProject: 'Erstelle...',
    duplicateProject: 'Duplizieren',
    shareProject: 'Teilen',
    deleteProject: 'Löschen',
    openProject: 'Projekt öffnen',
    confirmDelete: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    projectCreated: 'Projekt erfolgreich erstellt',
    projectDeleted: 'Projekt erfolgreich gelöscht',
    viewProject: 'Projekt anzeigen',
    projectImages: 'Projektbilder',
    projectSelection: 'Projektauswahl',
    selectProject: 'Projekt auswählen',
    imageDeleted: 'Bild erfolgreich gelöscht',
    deleteImageError: 'Löschen des Bildes fehlgeschlagen',
    deleteImageFailed: 'Bildlöschung fehlgeschlagen',
    imagesQueuedForSegmentation:
      '{{count}} Bilder zur Segmentierungswarteschlange hinzugefügt',
    imageQueuedForResegmentation:
      'Bild zur Re-Segmentierung in die Warteschlange eingereiht',
    allImagesAlreadySegmented:
      'Alle Bilder sind bereits segmentiert oder in der Warteschlange',
    errorAddingToQueue: 'Fehler beim Hinzufügen zur Warteschlange',
    imageAlreadyProcessing: 'Bild wird bereits verarbeitet',
    processImageFailed: 'Bildverarbeitung fehlgeschlagen',
    selected: '{{count}} ausgewählt',
    deleteSelected: 'Ausgewählte löschen',
    segmentationCompleted: 'Segmentierung für Bild abgeschlossen',
    segmentationFailed: 'Segmentierung fehlgeschlagen',
    segmentationStarted: 'Segmentierung hat begonnen',
    segmentationCompleteWithCount:
      'Segmentierung abgeschlossen! {{count}} Objekte gefunden',
    failedToLoadProjects: 'Laden der Projekte fehlgeschlagen',
    projectNameRequired: 'Bitte geben Sie einen Projektnamen ein',
    mustBeLoggedIn: 'Sie müssen angemeldet sein, um ein Projekt zu erstellen',
    failedToCreateProject: 'Projekterstellung fehlgeschlagen',
    serverResponseInvalid: 'Serverantwort war ungültig',
    projectCreatedDesc: '"{{name}}" ist bereit für Bilder',
    descriptionOptional: 'Beschreibung (Optional)',
    noDescriptionProvided: 'Keine Beschreibung angegeben',
    deleteDialog: {
      title: 'Löschen bestätigen',
      description:
        'Sind Sie sicher, dass Sie {{count}} ausgewählte Bilder löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    },
    selectProjectHeader: 'Projekt Auswählen',
    noProjects: 'Keine Projekte gefunden',
  },
  errors: {
    noProjectOrUser: 'Fehler: Kein Projekt oder Benutzer ausgewählt',
    unknown: 'Unbekannter Fehler',
    network:
      'Netzwerkverbindungsfehler. Bitte überprüfen Sie Ihre Internetverbindung.',
    unauthorized: 'Zugriff verweigert. Bitte melden Sie sich erneut an.',
    forbidden: 'Sie haben keine Berechtigung für diese Aktion.',
    notFound: 'Die angeforderte Ressource wurde nicht gefunden.',
    conflict:
      'Diese E-Mail ist bereits registriert. Versuchen Sie sich anzumelden oder verwenden Sie eine andere E-Mail.',
    invalidCredentials:
      'Ungültige E-Mail oder Passwort. Bitte überprüfen Sie Ihre Anmeldedaten.',
    validation: 'Validierungsfehler. Bitte überprüfen Sie Ihre Eingabe.',
    general:
      'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
    server: 'Serverfehler. Bitte versuchen Sie es später erneut.',
    timeout: 'Anfrage-Timeout. Bitte versuchen Sie es erneut.',
    sessionExpired:
      'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an, um fortzufahren.',
    tooManyRequests:
      'Zu viele Anfragen. Bitte warten Sie einen Moment und versuchen Sie es erneut.',
    serverUnavailable:
      'Dienst vorübergehend nicht verfügbar. Bitte versuchen Sie es in einigen Minuten erneut.',
    clientError:
      'Anfragefehler. Bitte überprüfen Sie Ihre Eingabe und versuchen Sie es erneut.',
    emailAlreadyExists:
      'Diese E-Mail ist bereits registriert. Versuchen Sie sich anzumelden oder verwenden Sie eine andere E-Mail.',
    validationErrors: {
      projectNameRequired: 'Bitte geben Sie einen Projektnamen ein',
      loginRequired: 'Sie müssen angemeldet sein, um ein Projekt zu erstellen',
      emailRequired: 'E-Mail ist erforderlich',
      passwordRequired: 'Passwort ist erforderlich',
      invalidEmail: 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
      passwordTooShort: 'Das Passwort muss mindestens 6 Zeichen lang sein',
      passwordsDoNotMatch: 'Die Passwörter stimmen nicht überein',
      confirmationRequired: 'Bitte bestätigen Sie Ihre Aktion',
      fieldRequired: 'Dieses Feld ist erforderlich',
    },
    operations: {
      loadProject:
        'Das Projekt konnte nicht geladen werden. Überprüfen Sie Ihre Verbindung und versuchen Sie es erneut.',
      saveProject:
        'Projektänderungen konnten nicht gespeichert werden. Bitte versuchen Sie es erneut.',
      uploadImage:
        'Das Bild konnte nicht hochgeladen werden. Überprüfen Sie das Dateiformat und die Größe.',
      deleteImage:
        'Das Bild kann nicht gelöscht werden. Versuchen Sie die Seite zu aktualisieren und die Aktion zu wiederholen.',
      processImage:
        'Bildverarbeitung fehlgeschlagen. Versuchen Sie ein anderes Bild oder kontaktieren Sie den Support.',
      segmentation:
        'Segmentierung fehlgeschlagen. Versuchen Sie ein anderes Modell oder passen Sie die Einstellungen an.',
      export:
        'Datenexport fehlgeschlagen. Überprüfen Sie, ob Daten verfügbar sind.',
      login:
        'Anmeldung fehlgeschlagen. Überprüfen Sie Ihre E-Mail und Ihr Passwort.',
      logout:
        'Abmeldung fehlgeschlagen. Versuchen Sie, Ihren Browser zu schließen.',
      register:
        'Registrierung fehlgeschlagen. Diese E-Mail wird möglicherweise bereits verwendet.',
      updateProfile:
        'Das Profil konnte nicht aktualisiert werden. Überprüfen Sie die angegebenen Informationen.',
      changePassword:
        'Das Passwort konnte nicht geändert werden. Überprüfen Sie Ihr aktuelles Passwort.',
      deleteAccount:
        'Das Konto konnte nicht gelöscht werden. Kontaktieren Sie den Support für Hilfe.',
      resetPassword:
        'Passwort-Reset fehlgeschlagen. Überprüfen Sie die angegebene E-Mail-Adresse.',
      updateConsent:
        'Einwilligungseinstellungen konnten nicht aktualisiert werden. Bitte versuchen Sie es erneut.',
      unshareProject:
        'Projekt konnte nicht aus geteilten Projekten entfernt werden',
      deleteProject:
        'Das Projekt kann nicht gelöscht werden. Stellen Sie sicher, dass Sie die erforderlichen Berechtigungen haben.',
    },
    deleteImages: 'Fehler beim Löschen der ausgewählten Bilder',
    contexts: {
      dashboard: 'Dashboard-Fehler',
      project: 'Projekt-Fehler',
      image: 'Bild-Fehler',
      segmentation: 'Segmentierung-Fehler',
      export: 'Export-Fehler',
      auth: 'Authentifizierung-Fehler',
      profile: 'Profil-Fehler',
      settings: 'Einstellungen-Fehler',
    },
  },
  images: {
    uploadImages: 'Bilder hochladen',
    dragDrop: 'Bilder hier hineinziehen',
    clickToSelect: 'oder klicken Sie, um Dateien auszuwählen',
    acceptedFormats: 'Akzeptierte Formate: JPEG, PNG, TIFF, BMP (max 10MB)',
    uploadProgress: 'Upload-Fortschritt',
    uploadingTo: 'Wählen Sie zuerst ein Projekt aus',
    currentProject: 'Aktuelles Projekt',
    autoSegment: 'Bilder nach Upload automatisch segmentieren',
    uploadCompleted: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    imagesUploaded: 'Bilder erfolgreich hochgeladen',
    imagesFailed: 'Hochladen der Bilder fehlgeschlagen',
    viewAnalyses: 'Analysen anzeigen',
    noAnalysesYet: 'Noch keine Analysen',
    runAnalysis: 'Analyse ausführen',
    viewResults: 'Ergebnisse anzeigen',
    dropImagesHere: 'Bilder hier ablegen...',
    selectProjectFirst: 'Bitte wählen Sie zuerst ein Projekt aus',
    projectRequired:
      'Sie müssen ein Projekt auswählen, bevor Sie Bilder hochladen können',
    pending: 'Ausstehend',
    uploading: 'Hochladen',
    processing: 'Verarbeitung',
    complete: 'Abgeschlossen',
    error: 'Fehler',
    imageDeleted: 'Bild erfolgreich gelöscht',
    deleteImageFailed: 'Löschen des Bildes fehlgeschlagen',
    deleteImageError: 'Fehler beim Löschen des Bildes',
    imageAlreadyProcessing: 'Bild wird bereits verarbeitet',
    processImageFailed: 'Bildverarbeitung fehlgeschlagen',
  },
  settings: {
    pageTitle: 'Einstellungen',
    profile: 'Profil',
    account: 'Konto',
    models: 'Modelle',
    manageSettings: 'Ihre Kontoeinstellungen verwalten',
    appearance: 'Erscheinungsbild',
    themeSettings: 'Design-Einstellungen',
    systemDefault: 'Systemstandard',
    languageSettings: 'Spracheinstellungen',
    selectLanguage: 'Sprache auswählen',
    accountSettings: 'Kontoeinstellungen',
    notificationSettings: 'Benachrichtigungseinstellungen',
    emailNotifications: 'E-Mail-Benachrichtigungen',
    pushNotifications: 'Push-Benachrichtigungen',
    profileSettings: 'Profileinstellungen',
    profileUpdated: 'Profil erfolgreich aktualisiert',
    profileUpdateFailed: 'Profilaktualisierung fehlgeschlagen',
    saveChanges: 'Änderungen speichern',
    savingChanges: 'Speichere...',
    notifications: {
      projectUpdates: 'Projekt-Updates',
      analysisCompleted: 'Analyse abgeschlossen',
      newFeatures: 'Neue Funktionen',
      marketingEmails: 'Marketing-E-Mails',
      billing: 'Rechnungsbenachrichtigungen',
    },
    personal: 'Persönliche Informationen',
    fullName: 'Vollständiger Name',
    organization: 'Organisation',
    department: 'Abteilung',
    publicProfile: 'Öffentliches Profil',
    bio: 'Biografie',
    makeProfileVisible: 'Mein Profil für andere Forscher sichtbar machen',
    dangerZone: 'Gefahrenbereich',
    deleteAccountWarning:
      'Sobald Sie Ihr Konto löschen, gibt es kein Zurück. Alle Ihre Daten werden dauerhaft gelöscht.',
    currentPassword: 'Aktuelles Passwort',
    newPassword: 'Neues Passwort',
    confirmNewPassword: 'Neues Passwort bestätigen',
    modelSelection: {
      title: 'Modellauswahl',
      description: 'Wählen Sie das KI-Modell für die Zellsegmentierung',
      models: {
        hrnet: {
          name: 'HRNet',
          description:
            'Schnelles und effizientes Modell für Echzeit-Segmentierung',
        },
        cbam: {
          name: 'CBAM-ResUNet',
          description:
            'Präzises Segmentierungsmodell mit Aufmerksamkeitsmechanismen',
        },
        unet_spherohq: {
          name: 'UNet (SpheroHQ)',
          description:
            'Beste Leistung auf SpheroHQ-Datensatz - optimiert für Sphäroid-Segmentierung mit ausgewogener Geschwindigkeit und Genauigkeit (~0.25s/Bild, 10 Bilder/s)',
        },
      },
    },
    confidenceThreshold: 'Vertrauensschwelle',
    confidenceThresholdDescription:
      'Mindestvertrauen für Segmentierungsvorhersagen erforderlich',
    detectHoles: 'Löcher Erkennen',
    detectHolesDescription:
      'Erkennung von inneren Strukturen und Löchern in Zellen aktivieren',
    currentThreshold: 'Aktuelle Schwelle',
    modelSelected: 'Modell erfolgreich ausgewählt',
    modelSettingsSaved: 'Modelleinstellungen erfolgreich gespeichert',
    modelSize: {
      small: 'Klein',
      medium: 'Mittel',
      large: 'Groß',
    },
    modelDescription: {
      hrnet:
        'Ausgewogenes Modell mit guter Geschwindigkeit und Qualität (E2E ~309ms, 4.9 Bilder/s)',
      cbam_resunet:
        'Präziseste Segmentierung mit Aufmerksamkeitsmechanismen (E2E ~482ms, 2.7 Bilder/s)',
      unet_spherohq:
        'Schnellstes Modell nach Optimierungen! Hervorragend für Echtzeitverarbeitung (E2E ~286ms, 5.5 Bilder/s)',
    },
    dataUsageTitle: 'Datennutzung und Datenschutz',
    dataUsageDescription:
      'Kontrollieren Sie, wie Ihre Daten für maschinelles Lernen und Forschung verwendet werden',
    allowMLTraining: {
      label: 'ML-Modell-Training erlauben',
      description:
        'Erlauben Sie die Nutzung Ihrer Daten zum Training und zur Verbesserung unserer Segmentierungsmodelle',
    },
    consent: {
      privacyNotice:
        'Ihr Datenschutz ist uns wichtig. Diese Einstellungen steuern, wie Ihre hochgeladenen Bilder und Segmentierungsdaten zur Verbesserung unserer ML-Modelle verwendet werden können. Sie können diese Präferenzen jederzeit ändern.',
      dataUsageNote:
        'Daten von Benutzern, die sich abgemeldet haben, werden nicht in Trainingspipelines aufgenommen.',
      algorithmImprovement: {
        label: 'Algorithmus-Verbesserung',
        description:
          'Daten zur Verbesserung der Segmentierungsgenauigkeit und -geschwindigkeit verwenden',
      },
      featureDevelopment: {
        label: 'Funktionsentwicklung',
        description:
          'Helfen Sie bei der Entwicklung neuer Funktionen und Fähigkeiten',
      },
      lastUpdated: 'Zuletzt aktualisiert',
      savePreferences: 'Einwilligungspräferenzen speichern',
      savingPreferences: 'Speichere...',
    },
    cancel: 'Abbrechen',
    deleting: 'Lösche...',
    deleteAccount: 'Konto Löschen',
    accountDeleted: 'Konto erfolgreich gelöscht',
    deleteAccountError: 'Fehler beim Löschen des Kontos',
    deleteAccountDialog: {
      title: 'Konto löschen',
      description:
        'Diese Aktion kann nicht rückgängig gemacht werden. Dies wird Ihr Konto dauerhaft löschen und alle Ihre Daten von unseren Servern entfernen.',
      whatWillBeDeleted: 'Was wird gelöscht:',
      deleteItems: {
        account: 'Ihr Benutzerkonto und Profil',
        projects: 'Alle Ihre Projekte und Bilder',
        segmentation: 'Alle Segmentierungsdaten und Ergebnisse',
        settings: 'Kontoeinstellungen und Präferenzen',
      },
      confirmationLabel: 'Bitte tippen Sie {email} zur Bestätigung:',
      confirmationPlaceholder: '{email}',
    },
    fillAllFields: 'Bitte füllen Sie alle erforderlichen Felder aus',
    passwordsDoNotMatch: 'Passwörter stimmen nicht überein',
    passwordTooShort: 'Passwort muss mindestens 6 Zeichen lang sein',
    passwordChanged: 'Passwort erfolgreich geändert',
    passwordsMatch: 'Passwörter stimmen überein',
    changingPassword: 'Passwort wird geändert...',
    changePassword: 'Passwort Ändern',
    languageUpdated: 'Sprache erfolgreich aktualisiert',
    themeUpdated: 'Design erfolgreich aktualisiert',
    appearanceDescription: 'Passen Sie das Erscheinungsbild der Anwendung an',
    language: 'Sprache',
    languageDescription: 'Wählen Sie Ihre bevorzugte Sprache',
    theme: 'Design',
    themeDescription: 'Wählen Sie helles, dunkles oder System-Design',
    light: 'Hell',
    dark: 'Dunkel',
    system: 'System',
  },
  auth: {
    signIn: 'Anmelden',
    signUp: 'Registrieren',
    redirectingToDashboard: 'Weiterleitung zum Dashboard...',
    signOut: 'Abmelden',
    forgotPassword: 'Passwort vergessen?',
    resetPassword: 'Passwort zurücksetzen',
    dontHaveAccount: 'Sie haben kein Konto?',
    alreadyHaveAccount: 'Sie haben bereits ein Konto?',
    signInWith: 'Anmelden mit',
    signUpWith: 'Registrieren mit',
    orContinueWith: 'oder fortfahren mit',
    rememberMe: 'Angemeldet bleiben',
    emailRequired: 'E-Mail ist erforderlich',
    passwordRequired: 'Passwort ist erforderlich',
    invalidEmail: 'Ungültige E-Mail-Adresse',
    passwordTooShort: 'Passwort muss mindestens 6 Zeichen lang sein',
    passwordsDontMatch: 'Passwörter stimmen nicht überein',
    successfulSignIn: 'Erfolgreich angemeldet',
    successfulSignUp: 'Registrierung erfolgreich',
    verifyEmail: 'Bitte überprüfen Sie Ihre E-Mail, um Ihr Konto zu bestätigen',
    successfulSignOut: 'Erfolgreich abgemeldet',
    checkingAuthentication: 'Authentifizierung prüfen...',
    loadingAccount: 'Ihr Konto wird geladen...',
    processingRequest: 'Ihre Anfrage wird bearbeitet...',
    signInToAccount: 'Bei Ihrem Konto anmelden',
    accessPlatform: 'Zugang zur Sphäroid-Segmentierungsplattform',
    emailAddress: 'E-Mail-Adresse',
    emailPlaceholder: '<EMAIL>',
    password: 'Passwort',
    passwordPlaceholder: '••••••••',
    signingIn: 'Anmeldung läuft...',
    fillAllFields: 'Bitte füllen Sie alle Felder aus',
    signInSuccess: 'Erfolgreich angemeldet',
    signInFailed: 'Anmeldung fehlgeschlagen',
    registrationSuccess: 'Registrierung erfolgreich',
    registrationFailed: 'Registrierung fehlgeschlagen',
    logoutFailed: 'Abmeldung fehlgeschlagen',
    profileUpdateFailed: 'Profilaktualisierung fehlgeschlagen',
    welcomeMessage: 'Willkommen bei der Sphäroid-Segmentierungsplattform',
    confirmationRequired:
      'Bestätigungstext ist erforderlich und muss Ihrer E-Mail-Adresse entsprechen',
    agreeToTerms: 'Durch die Anmeldung stimmen Sie unseren',
    termsOfService: 'Nutzungsbedingungen',
    and: 'und',
    privacyPolicy: 'Datenschutzrichtlinie',
    createAccount: 'Erstellen Sie Ihr Konto',
    signUpPlatform:
      'Registrieren Sie sich, um die Sphäroid-Segmentierungsplattform zu nutzen',
    confirmPassword: 'Passwort bestätigen',
    passwordsMatch: 'Passwörter stimmen überein',
    passwordsDoNotMatch: 'Passwörter stimmen nicht überein',
    agreeToTermsCheckbox: 'Ich stimme den',
    mustAgreeToTerms:
      'Sie müssen den Allgemeinen Geschäftsbedingungen zustimmen',
    creatingAccount: 'Konto wird erstellt...',
    alreadyLoggedIn: 'Sie sind bereits angemeldet',
    alreadySignedUp: 'Sie sind bereits registriert und angemeldet.',
    goToDashboard: 'Zum Dashboard gehen',
    signUpFailed: 'Registrierung fehlgeschlagen',
    enterEmailForReset: 'E-Mail-Adresse für Passwort-Reset eingeben',
    sending: 'Senden...',
    sendNewPassword: 'Neues Passwort senden',
    emailSent: 'E-Mail gesendet',
    checkEmailForNewPassword:
      'Überprüfen Sie Ihre E-Mail für den Passwort-Reset-Link',
    resetPasswordEmailSent: 'Passwort-Reset-E-Mail gesendet',
    resetPasswordError: 'Fehler beim Zurücksetzen des Passworts',
    backToSignIn: 'Zurück zur Anmeldung',
    didntReceiveEmail: 'E-Mail nicht erhalten?',
    rememberPassword: 'Passwort wieder eingefallen?',
    redirectingToSignIn: 'Redirecting to sign-in...',
    tryAgain: 'Erneut versuchen',
  },
  profile: {
    title: 'Profil',
    about: 'Über',
    activity: 'Aktivität',
    projects: 'Projekte',
    papers: 'Artikel',
    analyses: 'Analysen',
    recentProjects: 'Aktuelle Projekte',
    recentAnalyses: 'Aktuelle Analysen',
    accountDetails: 'Kontodetails',
    accountType: 'Kontotyp',
    joinDate: 'Beitrittsdatum',
    lastActive: 'Zuletzt aktiv',
    projectsCreated: 'Erstellte Projekte',
    imagesUploaded: 'Hochgeladene Bilder',
    segmentationsCompleted: 'Abgeschlossene Segmentierungen',
    editProfile: 'Profil bearbeiten',
    joined: 'Beigetreten',
    copyApiKey: 'API-Schlüssel kopieren',
    collaborators: 'Mitarbeiter',
    noCollaborators: 'Keine Mitarbeiter',
    connectedAccounts: 'Verbundene Konten',
    connect: 'Verbinden',
    recentActivity: 'Aktuelle Aktivität',
    noRecentActivity: 'Keine aktuelle Aktivität',
    statistics: 'Statistiken',
    totalImagesProcessed: 'Verarbeitete Bilder gesamt',
    averageProcessingTime: 'Durchschnittliche Verarbeitungszeit',
    fromLastMonth: 'vom letzten Monat',
    storageUsed: 'Verwendeter Speicher',
    of: 'von',
    apiRequests: 'API-Anfragen',
    thisMonth: 'diesen Monat',
    recentPublications: 'Aktuelle Veröffentlichungen',
    viewAll: 'Alle anzeigen',
    noPublications: 'Noch keine Veröffentlichungen',
    today: 'heute',
    yesterday: 'gestern',
    daysAgo: 'Tage her',
    completionRate: 'Abschlussrate',
    createdProject: 'Projekt erstellt',
    completedSegmentation: 'Segmentierung abgeschlossen für',
    uploadedImage: 'Bild hochgeladen',
    avatar: {
      uploadButton: 'Avatar hochladen',
      selectFile: 'Avatar-Bild auswählen',
      cropTitle: 'Avatar zuschneiden',
      cropDescription:
        'Schneiden Sie Ihr Avatar-Bild für die perfekte Darstellung zu',
      zoomLevel: 'Zoomstufe',
      cropInstructions:
        'Ziehen zum Verschieben, Schieberegler zum Zoomen verwenden',
      applyChanges: 'Änderungen übernehmen',
      processing: 'Verarbeitung läuft...',
      invalidFileType: 'Ungültiger Dateityp. Bitte wählen Sie eine Bilddatei.',
      fileTooLarge: 'Datei zu groß. Maximale Größe ist 5MB.',
      cropError:
        'Fehler beim Verarbeiten des Bildes. Bitte versuchen Sie es erneut.',
      uploadSuccess: 'Avatar erfolgreich hochgeladen',
      uploadError:
        'Fehler beim Hochladen des Avatars. Bitte versuchen Sie es erneut.',
    },
  },
  segmentation: {
    mode: {
      view: 'Anzeigen und navigieren',
      edit: 'Bearbeiten',
      editVertices: 'Eckpunkte bearbeiten',
      addPoints: 'Punkte hinzufügen',
      create: 'Erstellen',
      createPolygon: 'Polygon erstellen',
      slice: 'Schneiden',
      delete: 'Löschen',
      deletePolygon: 'Polygon löschen',
      unknown: 'Unbekannt',
    },
    modeDescription: {
      view: 'Navigieren und Polygone auswählen',
      edit: 'Eckpunkte bewegen und modifizieren',
      addPoints: 'Punkte zwischen Eckpunkten hinzufügen',
      create: 'Neue Polygone erstellen',
      slice: 'Polygone mit einer Linie teilen',
      delete: 'Polygone entfernen',
    },
    toolbar: {
      mode: 'Modus',
      keyboard: 'Taste: {{key}}',
      requiresSelection: 'Erfordert Polygon-Auswahl',
      requiresPolygonSelection: 'Erfordert Polygon-Auswahl',
      select: 'Auswählen',
      undoTooltip: 'Rückgängig (Strg+Z)',
      undo: 'Rückgängig',
      redoTooltip: 'Wiederholen (Strg+Y)',
      redo: 'Wiederholen',
      zoomInTooltip: 'Vergrößern (+)',
      zoomIn: 'Vergrößern',
      zoomOutTooltip: 'Verkleinern (-)',
      zoomOut: 'Verkleinern',
      resetViewTooltip: 'Ansicht zurücksetzen (R)',
      resetView: 'Zurücksetzen',
      unsavedChanges: 'Nicht gespeicherte Änderungen',
      saving: 'Speichern...',
      save: 'Speichern',
      keyboardShortcuts:
        'V: Anzeigen • E: Bearbeiten • A: Hinzufügen • N: Neu • S: Schneiden • D: Löschen',
      nothingToSave: 'Alle Änderungen gespeichert',
    },
    status: {
      polygons: 'Polygone',
      vertices: 'Eckpunkte',
      visible: 'sichtbar',
      hidden: 'versteckt',
      selected: 'ausgewählt',
      saved: 'Gespeichert',
      unsaved: 'Nicht gespeichert',
      noPolygons: 'Keine Polygone',
      startCreating: 'Beginnen Sie mit der Erstellung eines Polygons',
      polygonList: 'Polygon-Liste',
      external: 'External',
      internal: 'Internal',
    },
    shortcuts: {
      buttonText: 'Tastenkürzel',
      title: 'Tastenkürzel',
      dialogTitle: 'Tastenkürzel',
      footerNote:
        'Diese Tastenkürzel funktionieren im Segmentierungseditor für schnelleres und bequemeres Arbeiten.',

      // Categories
      categories: {
        modes: 'Bearbeitungsmodi',
        actions: 'Aktionen',
        view: 'Ansichtssteuerungen',
        navigation: 'Navigation',
      },

      // Mode shortcuts
      viewMode: 'Ansichtsmodus',
      editVertices: 'Eckpunkte bearbeiten-Modus',
      addPoints: 'Punkte hinzufügen-Modus',
      createPolygon: 'Neues Polygon erstellen',
      sliceMode: 'Schnitt-Modus',
      deleteMode: 'Lösch-Modus',

      // Action shortcuts
      save: 'Speichern',
      undo: 'Rückgängig',
      redo: 'Wiederholen',
      deleteSelected: 'Ausgewähltes Polygon löschen',

      // View shortcuts
      zoom: 'Vergrößern/Verkleinern',
      resetView: 'Ansicht zurücksetzen',
      fitToScreen: 'An Bildschirm anpassen',

      // Navigation shortcuts
      cycleModes: 'Modi durchschalten',
      cycleModesReverse: 'Modi durchschalten (rückwärts)',
      cancel: 'Aktuelle Operation abbrechen',
      showHelp: 'Diese Hilfe anzeigen',

      // Conditions
      requiresSelection: 'Erfordert Polygon-Auswahl',

      // Legacy keys (kept for backward compatibility)
      v: 'Ansichtsmodus',
      e: 'Eckpunkte bearbeiten-Modus',
      a: 'Punkte hinzufügen-Modus',
      n: 'Neues Polygon erstellen',
      s: 'Schnitt-Modus',
      d: 'Lösch-Modus',
      shift: 'Halten für automatisches Hinzufügen von Punkten',
      ctrlZ: 'Rückgängig',
      ctrlY: 'Wiederholen',
      delete: 'Ausgewähltes Polygon löschen',
      esc: 'Aktuelle Operation abbrechen',
      plus: 'Vergrößern',
      minus: 'Verkleinern',
      r: 'Ansicht zurücksetzen',
    },
    tips: {
      header: 'Tipps:',
      edit: {
        createPoint: 'Klicken, um einen neuen Punkt zu erstellen',
        holdShift:
          'Shift halten, um automatisch eine Punktsequenz zu erstellen',
        closePolygon: 'Polygon durch Klicken auf den ersten Punkt schließen',
      },
      slice: {
        startSlice: 'Klicken, um den Schnitt zu beginnen',
        endSlice: 'Nochmals klicken, um den Schnitt zu beenden',
        cancelSlice: 'Esc bricht den Schnitt ab',
      },
      addPoints: {
        hoverLine: 'Cursor über die Polygonlinie bewegen',
        clickAdd: 'Klicken, um Punkt zum ausgewählten Polygon hinzuzufügen',
        escCancel: 'Esc beendet den Hinzufüge-Modus',
      },
    },
    helpTips: {
      editMode: [
        'Klicken, um einen neuen Punkt zu erstellen',
        'Shift halten, um automatisch eine Punktsequenz zu erstellen',
        'Polygon durch Klicken auf den ersten Punkt schließen',
      ],
      slicingMode: [
        'Klicken, um den Schnitt zu beginnen',
        'Nochmals klicken, um den Schnitt zu beenden',
        'Esc bricht den Schnitt ab',
      ],
      pointAddingMode: [
        'Cursor über die Polygonlinie bewegen',
        'Klicken, um Punkt zum ausgewählten Polygon hinzuzufügen',
        'Esc verlässt den Hinzufüge-Modus',
      ],
    },
    loading: 'Segmentierung wird geladen...',
    noPolygons: 'Keine Polygone gefunden',
    polygonNotFound: 'Polygon nicht gefunden',
    invalidSlice: 'Ungültige Schnitt-Operation',
    sliceSuccess: 'Polygon erfolgreich geschnitten',
    sliceFailed: 'Polygon-Schnitt fehlgeschlagen',
    instructions: {
      slice: {
        selectPolygon:
          '1. Klicken Sie auf ein Polygon, um es zum Schneiden auszuwählen',
        placeFirstPoint:
          '2. Klicken Sie, um den ersten Schnittpunkt zu platzieren',
        placeSecondPoint:
          '3. Klicken Sie, um den zweiten Schnittpunkt zu platzieren und den Schnitt durchzuführen',
        cancel: 'Drücken Sie ESC zum Abbrechen',
      },
      create: {
        startPolygon:
          '1. Klicken Sie, um mit der Polygon-Erstellung zu beginnen',
        continuePoints:
          '2. Klicken Sie weiter, um mehr Punkte hinzuzufügen (mindestens 3 benötigt)',
        finishPolygon:
          '3. Fügen Sie weiter Punkte hinzu oder klicken Sie nahe dem ersten Punkt, um das Polygon zu schließen',
        holdShift: 'SHIFT halten für automatisches Hinzufügen von Punkten',
        cancel: 'Drücken Sie ESC zum Abbrechen',
      },
      addPoints: {
        clickVertex:
          'Klicken Sie auf einen beliebigen Eckpunkt, um mit dem Hinzufügen von Punkten zu beginnen',
        addPoints:
          'Klicken Sie, um Punkte hinzuzufügen, dann klicken Sie auf einen anderen Eckpunkt zum Abschließen. Klicken Sie direkt auf einen anderen Eckpunkt ohne Punkte hinzuzufügen, um alle Punkte dazwischen zu entfernen.',
        holdShift: 'SHIFT halten für automatisches Hinzufügen von Punkten',
        cancel: 'Drücken Sie ESC zum Abbrechen',
      },
      editVertices: {
        selectPolygon:
          'Klicken Sie auf ein Polygon, um es zur Bearbeitung auszuwählen',
        dragVertices: 'Klicken und ziehen Sie Eckpunkte, um sie zu bewegen',
        addPoints:
          'SHIFT halten und auf einen Eckpunkt klicken, um Punkte hinzuzufügen',
        deleteVertex: 'Doppelklick auf einen Eckpunkt, um ihn zu löschen',
      },
      deletePolygon: {
        clickToDelete: 'Klicken Sie auf ein Polygon, um es zu löschen',
      },
      view: {
        selectPolygon: 'Klicken Sie auf ein Polygon, um es auszuwählen',
        navigation: 'Ziehen zum Schwenken • Scrollen zum Zoomen',
      },
      modes: {
        slice: 'Schnitt-Modus',
        create: 'Polygon-Erstellungs-Modus',
        addPoints: 'Punkte-Hinzufüge-Modus',
        editVertices: 'Eckpunkt-Bearbeitungs-Modus',
        deletePolygon: 'Polygon-Lösch-Modus',
        view: 'Ansichts-Modus',
      },
      shiftIndicator: '⚡ SHIFT: Automatisches Hinzufügen von Punkten',
    },
  },
  status: {
    segmented: 'Segmentiert',
    processing: 'Verarbeitung',
    queued: 'In Warteschlange',
    failed: 'Fehlgeschlagen',
    no_segmentation: 'Keine Segmentierung',
    disconnected: 'Vom Server getrennt',
    error: 'ML-Service-Fehler',
    ready: 'Bereit für Segmentierung',
    online: 'Online',
    offline: 'Offline',
    noPolygons: 'Keine Polygone',
  },
  queue: {
    title: 'Segmentierungs-Warteschlange',
    connected: 'Verbunden',
    disconnected: 'Getrennt',
    waiting: 'wartend',
    processing: 'verarbeitend',
    segmentAll: 'Alle Segmentieren',
    segmentAllWithCount: 'Alle Segmentieren ({{count}})',
    totalProgress: 'Gesamtfortschritt',
    images: 'Bilder',
    loadingStats: 'Statistiken werden geladen...',
    connectingMessage:
      'Verbindung zum Server... Echtzeit-Updates werden bald verfügbar sein.',
    emptyMessage:
      'Keine Bilder in der Warteschlange. Laden Sie Bilder hoch und fügen Sie sie zur Segmentierung hinzu.',
    addingToQueue: 'Zur Warteschlange hinzufügen...',
    resegmentSelected: 'Ausgewählte re-segmentieren ({{count}})',
    segmentMixed:
      'Segmentiere {{new}} + Re-segmentiere {{resegment}} ({{total}} gesamt)',
    segmentTooltip:
      '{{new}} neue Bilder werden segmentiert, {{resegment}} ausgewählte Bilder werden re-segmentiert',
  },
  toast: {
    error: 'Ein Fehler ist aufgetreten',
    success: 'Operation erfolgreich',
    info: 'Information',
    warning: 'Warnung',
    loading: 'Lädt...',
    failedToUpdate:
      'Aktualisierung der Daten fehlgeschlagen. Bitte erneut versuchen.',
    fillAllFields: 'Bitte füllen Sie alle Felder aus',
    operationFailed: 'Operation fehlgeschlagen. Bitte erneut versuchen.',
    unexpectedError: 'Unerwarteter Fehler',
    somethingWentWrong:
      'Etwas ist schiefgelaufen. Bitte versuchen Sie es später erneut.',
    somethingWentWrongPage:
      'Beim Laden dieser Seite ist ein Fehler aufgetreten.',
    returnToHome: 'Zurück zur Startseite',
    operationCompleted: 'Operation erfolgreich abgeschlossen',
    dataSaved: 'Daten erfolgreich gespeichert',
    dataUpdated: 'Daten erfolgreich aktualisiert',
    reconnecting: 'Verbinde erneut mit Server...',
    reconnected: 'Verbindung zum Server wiederhergestellt',
    connectionFailed: 'Wiederherstellung der Serververbindung fehlgeschlagen',
    segmentationRequested: 'Segmentierungsanfrage übermittelt',
    segmentationCompleted: 'Bildsegmentierung abgeschlossen',
    segmentationFailed: 'Segmentierung fehlgeschlagen',
    segmentationResultFailed:
      'Abrufen des Segmentierungsergebnisses fehlgeschlagen',
    segmentationStatusFailed:
      'Überprüfung des Segmentierungsstatus fehlgeschlagen',
    exportCompleted: 'Export erfolgreich abgeschlossen!',
    exportFailed: 'Export fehlgeschlagen. Bitte erneut versuchen.',
    project: {
      created: 'Projekt erfolgreich erstellt',
      createFailed: 'Projekt konnte nicht erstellt werden',
      deleted: 'Projekt erfolgreich gelöscht',
      deleteFailed: 'Projekt konnte nicht gelöscht werden',
      urlCopied: 'Projekt-URL in die Zwischenablage kopiert',
      unshared: 'Projekt aus Geteilten entfernt',
      notFound: 'Projekt nicht gefunden',
      invalidResponse: 'Serverantwort war ungültig',
      readyForImages: 'ist bereit für Bilder',
      selected: '{{count}} Bild ausgewählt',
      selected_other: '{{count}} Bilder ausgewählt',
      deleteSelected: 'Ausgewählte löschen',
    },
    profile: {
      consentUpdated: 'Einverständniseinstellungen erfolgreich aktualisiert',
      loadFailed: 'Laden der Profildaten fehlgeschlagen',
    },
    upload: {
      failed: 'Aktualisierung der Bilder nach Upload fehlgeschlagen',
    },
    segmentation: {
      saved: 'Segmentierung erfolgreich gespeichert',
      failed: 'Segmentierung fehlgeschlagen',
      deleted: 'Polygon gelöscht',
      cannotDeleteVertex:
        'Kann Scheitelpunkt nicht löschen - Polygon benötigt mindestens 3 Punkte',
      vertexDeleted: 'Scheitelpunkt erfolgreich gelöscht',
      started: 'Segmentierung hat begonnen',
      completed: 'Segmentierung erfolgreich abgeschlossen',
      completedWithCount:
        'Segmentierung abgeschlossen! {{count}} Objekte gefunden',
      noPolygons: 'Keine Segmentierungspolygone erkannt',
      reloadFailed:
        'Laden der Segmentierungsergebnisse fehlgeschlagen. Bitte Seite aktualisieren.',
      autosaveFailed:
        'Automatisches Speichern fehlgeschlagen - Änderungen können verloren gehen',
    },
  },
  project: {
    selected: '{{count}} Bild ausgewählt',
    selected_other: '{{count}} Bilder ausgewählt',
    deleteSelected: 'Ausgewählte löschen',
  },
  export: {
    advancedExport: 'Erweiterter Export',
    advancedOptions: 'Erweiterte Export-Optionen',
    configureSettings:
      'Konfigurieren Sie Ihre Export-Einstellungen, um ein umfassendes Datenpaket zu erstellen',
    general: 'Allgemein',
    visualization: 'Visualisierung',
    exportContents: 'Export-Inhalte',
    selectContent:
      'Wählen Sie aus, welche Inhaltstypen in Ihren Export einbezogen werden sollen',
    includeOriginal: 'Originalbilder einschließen',
    includeVisualizations:
      'Visualisierungen mit nummerierten Polygonen einschließen',
    includeDocumentation: 'Dokumentation und Metadaten einschließen',
    selectedImages: 'Ausgewählte Bilder',
    imagesSelected: '{{count}} von {{total}} Bildern ausgewählt',
    selectAll: 'Alle Auswählen',
    selectNone: 'Nichts Auswählen',
    imageSelection: 'Bildauswahl',
    chooseImages:
      'Wählen Sie aus, welche Bilder in den Export einbezogen werden sollen',
    searchImages: 'Bilder suchen...',
    sortBy: 'Sortieren nach',
    sortOptions: {
      date: 'Datum',
      name: 'Name',
      status: 'Status',
    },
    showingImages: 'Zeige {{start}}-{{end}} von {{total}}',
    noImagesFound: 'Keine Bilder gefunden',
    qualitySettings: 'Qualitätseinstellungen',
    imageQuality: 'Bildqualität',
    compressionLevel: 'Komprimierungsgrad',
    outputResolution: 'Ausgabeauflösung',
    colorSettings: 'Farbeinstellungen',
    backgroundColor: 'Hintergrundfarbe',
    strokeColor: 'Strichfarbe',
    strokeWidth: 'Strichbreite',
    fontSize: 'Schriftgröße',
    showNumbers: 'Polygon-Nummern anzeigen',
    showLabels: 'Beschriftungen anzeigen',
    scaleConversion: 'Skalierungskonvertierung',
    pixelToMicrometerScale: 'Pixelgröße',
    scaleDescription:
      'Geben Sie an, wie viele Mikrometer ein Pixel repräsentiert, um Messungen umzurechnen',
    scalePlaceholder: 'z.B. 0.5 (1 Pixel = 0.5 µm)',
    scaleUnit: 'µm/Pixel',
    scaleWarning:
      'Hinweis: Skalierungswert über 1 µm/Pixel deutet auf sehr geringe Vergrößerung hin. Bitte überprüfen.',
    outputSettings: 'Ausgabeeinstellungen',
    exportFormatsLabel: 'Exportformate',
    exportFormats: {
      yolo: 'YOLO-Format',
      excel: 'Excel-Format',
      json: 'JSON-Format',
    },
    exportToZip: 'Als ZIP-Archiv exportieren',
    generateExcel: 'Excel-Metriken generieren',
    includeCocoFormat: 'COCO-Format-Annotationen einschließen',
    includeJsonMetadata: 'JSON-Metadaten einschließen',
    preparing: 'Export wird vorbereitet...',
    processing: 'Verarbeitung {{current}} von {{total}}',
    packaging: 'Paket wird erstellt...',
    completed: 'Export abgeschlossen',
    downloading: 'Herunterladen...',
    cancelled: 'Export abgebrochen',
    connected: 'Verbunden',
    disconnected: 'Getrennt',
    reconnecting: 'Verbinde neu...',
    startExport: 'Export Starten',
    cancel: 'Abbrechen',
    download: 'Herunterladen',
    retry: 'Wiederholen',
    close: 'Schließen',
    exportError: 'Export fehlgeschlagen',
    exportFailed: 'Export fehlgeschlagen',
    exportComplete: 'Export abgeschlossen',
    metricsExportComplete: 'Metriken-Export abgeschlossen',
    connectionError: 'Verbindung während Export verloren',
    serverError: 'Server-Fehler aufgetreten',
    invalidSelection: 'Bitte wählen Sie mindestens ein Bild aus',
    noData: 'Keine Daten für Export verfügbar',
    segmentationData: 'Segmentierungsdaten',
    spheroidMetrics: 'Sphäroid-Metriken',
    cocoFormat: 'COCO-Format',
    cocoFormatTitle: 'COCO-Format-Export',
    downloadJson: 'JSON herunterladen',
    formatsTab: 'Formate',
  },
  imageDeleted: 'Bild erfolgreich gelöscht',
  deleteImageFailed: 'Löschen des Bildes fehlgeschlagen',
  deleteImageError: 'Fehler beim Löschen des Bildes',
  imageAlreadyProcessing: 'Bild wird bereits verarbeitet',
  processImageFailed: 'Bildverarbeitung fehlgeschlagen',
  exportDialog: {
    title: 'Export-Optionen',
    includeMetadata: 'Metadaten einschließen',
    includeSegmentation: 'Segmentierung einschließen',
    includeObjectMetrics: 'Objekt-Metriken einschließen',
    exportMetricsOnly: 'Nur Metriken exportieren (XLSX)',
    selectImages: 'Bilder für Export auswählen',
    selectAll: 'Alle Auswählen',
    selectNone: 'Alle Abwählen',
    noImagesAvailable: 'Keine Bilder verfügbar',
  },
  docs: {
    badge: 'Dokumentation',
    title: 'SpheroSeg Dokumentation',
    subtitle:
      'Umfassender Leitfaden für unsere Sphäroid-Segmentierungsplattform',
    backTo: 'Zurück zu {{page}}',
    navigation: 'Navigation',
    nav: {
      introduction: 'Einführung',
      gettingStarted: 'Erste Schritte',
      uploadingImages: 'Bilder Hochladen',
      modelSelection: 'Modellauswahl',
      segmentationProcess: 'Segmentierungsprozess',
      segmentationEditor: 'Segmentierungseditor',
      exportFeatures: 'Export-Funktionen',
    },
    introduction: {
      title: 'Einführung',
      whatIs: 'Was ist SpheroSeg?',
      description:
        'SpheroSeg ist eine fortschrittliche Plattform für die Segmentierung und Analyse zellulärer Sphäroide in mikroskopischen Bildern.',
      developedBy: 'Diese Plattform wurde von Bc. Michal Průšek entwickelt.',
      addresses:
        'SpheroSeg adressiert die herausfordernde Aufgabe der präzisen Identifikation und Segmentierung.',
    },
    gettingStarted: {
      title: 'Erste Schritte',
      accountCreation: 'Konto Erstellen',
      accountDescription:
        'Um SpheroSeg zu verwenden, müssen Sie ein Konto erstellen.',
      accountSteps: {
        step1: 'Zur Registrierungsseite navigieren',
        step2: 'E-Mail-Adresse eingeben',
        step3: 'Profil vervollständigen',
        step4: 'E-Mail-Adresse verifizieren',
      },
      firstProject: 'Erstes Projekt Erstellen',
      projectDescription: 'Projekte helfen bei der Organisation.',
      projectSteps: {
        step1: 'Auf "Neues Projekt" klicken',
        step2: 'Name und Beschreibung eingeben',
        step3: 'Projekttyp auswählen',
        step4: 'Auf "Projekt Erstellen" klicken',
      },
    },
    uploadImages: {
      title: 'Bilder Hochladen',
      description: 'SpheroSeg unterstützt verschiedene Bildformate.',
      methods: 'Upload-Methoden',
      methodsDescription: 'Mehrere Wege zum Hochladen:',
      methodsList: {
        dragDrop: 'Dateien per Drag & Drop',
        browse: 'Durchsuchen und auswählen',
        batch: 'Batch-Upload',
      },
      note: 'Hinweis:',
      noteText: 'Stellen Sie guten Kontrast sicher.',
    },
    modelSelection: {
      title: 'Modellauswahl',
      description: 'SpheroSeg bietet drei verschiedene KI-Modelle.',
      models: {
        hrnet: {
          name: 'HRNet (Klein)',
          inferenceTime: 'E2E-Zeit: ~309ms pro Bild (ML-Inferenz: 204ms)',
          bestFor:
            'Am besten für: Ausgewogene Leistung zwischen Geschwindigkeit und Qualität',
          description: 'Schnelles und effizientes Modell.',
        },
        cbam: {
          name: 'CBAM-ResUNet (Mittel)',
          inferenceTime: 'E2E-Zeit: ~482ms pro Bild (ML-Inferenz: 377ms)',
          bestFor: 'Am besten für: Maximale Präzision bei der Segmentierung',
          description:
            'Präzises Segmentierungsmodell mit Aufmerksamkeitsmechanismen für genaue Sphäroid-Grenzerkennung.',
        },
        unet_spherohq: {
          name: 'UNet (SpheroHQ)',
          inferenceTime: 'E2E-Zeit: ~286ms pro Bild (ML-Inferenz: 181ms)',
          bestFor:
            'Am besten für: Schnellste Verarbeitung, hervorragend für Echtzeitanwendungen',
          description:
            'Spezialisiertes Modell, das spezifisch für Sphäroid-Segmentierung mit dem SpheroHQ-Datensatz trainiert wurde. Bietet ausgewogene Geschwindigkeit und Genauigkeit.',
        },
      },
      howToSelect: 'Modell Auswählen',
      selectionSteps: {
        step1: 'Projekt öffnen',
        step2: 'Modellauswahl-Menü finden',
        step3: 'Wählen Sie aus HRNet, CBAM-ResUNet oder UNet (SpheroHQ)',
        step4: 'Konfidenzschwellwert anpassen',
        step5: 'Auswahl wird gespeichert',
      },
      tip: 'Tipp:',
      tipText:
        'Verwenden Sie UNet für die schnellste Verarbeitung mit 5,5 Bildern/Sekunde Durchsatz. Wählen Sie CBAM-ResUNet für maximale Präzision bei Forschungsarbeiten. Wählen Sie HRNet für ausgewogene Leistung zwischen Geschwindigkeit und Qualität.',
    },
    segmentationProcess: {
      title: 'Segmentierungsprozess',
      description: 'Der Prozess verwendet fortschrittliche KI-Modelle.',
      queueBased: 'Warteschlangen-basierte Verarbeitung',
      queueDescription: 'SpheroSeg verwendet ein Warteschlangensystem.',
      queueFeatures: {
        realTime: 'Echtzeit-Status mit WebSocket',
        batch: 'Batch-Verarbeitung',
        priority: 'Prioritätsverwaltung',
        recovery: 'Automatische Fehlerwiederherstellung',
      },
      workflow: 'Automatischer Arbeitsablauf',
      workflowSteps: {
        step1: 'Bilder hochladen',
        step2: 'KI-Modell auswählen',
        step3: 'Konfidenzschwellwert anpassen',
        step4: 'Auf "Auto-Segmentierung" klicken',
        step5: 'Fortschritt in Echtzeit überwachen',
        step6: 'Ergebnisse überprüfen',
      },
      polygonTypes: 'Polygon-Typen',
      polygonDescription: 'Das System erkennt zwei Typen:',
      polygonTypesList: {
        external: 'Externe Polygone (grün)',
        internal: 'Interne Polygone (rot)',
      },
      processingNote: 'Zeiten variieren je nach Modell:',
      processingTimes: 'HRNet (~3s), CBAM-ResUNet (~7s).',
    },
    segmentationEditor: {
      title: 'Segmentierungseditor',
      description: 'Mächtiges Werkzeug zur Verfeinerung von Segmentierungen.',
      editingModes: 'Bearbeitungsmodi',
      modes: {
        view: {
          title: 'Ansichtsmodus',
          description: 'Navigieren und inspizieren ohne Änderungen.',
        },
        editVertices: {
          title: 'Eckpunkte Bearbeiten',
          description: 'Einzelne Eckpunkte ziehen.',
        },
        addPoints: {
          title: 'Punkte Hinzufügen',
          description: 'Neue Eckpunkte einfügen.',
        },
        createPolygon: {
          title: 'Polygon Erstellen',
          description: 'Neue Polygone zeichnen.',
        },
        sliceMode: {
          title: 'Schneidemodus',
          description: 'Polygone in Teile schneiden.',
        },
        deletePolygon: {
          title: 'Polygon Löschen',
          description: 'Unerwünschte Polygone entfernen.',
        },
      },
      keyFeatures: 'Hauptfunktionen',
      features: {
        undoRedo: 'Rückgängig/Wiederholen-System',
        autoSave: 'Automatisches Speichern',
        zoomPan: 'Zoom und Schwenken',
        polygonManagement: 'Polygon-Verwaltung',
        keyboardShortcuts: 'Tastenkürzel',
        realTimeFeedback: 'Echtzeit-Feedback',
      },
      shortcuts: 'Wichtige Tastenkürzel',
      shortcutCategories: {
        navigation: 'Navigation:',
        actions: 'Aktionen:',
      },
      shortcutsList: {
        v: 'Ansichtsmodus',
        e: 'Eckpunkte bearbeiten',
        a: 'Punkte hinzufügen',
        n: 'Polygon erstellen',
        ctrlZ: 'Rückgängig',
        ctrlY: 'Wiederholen',
        ctrlS: 'Speichern',
        delete: 'Ausgewählte löschen',
      },
      workingWithPolygons: 'Arbeiten mit Polygonen',
      polygonSteps: {
        step1: 'Polygon auswählen',
        step2: 'Zum entsprechenden Modus wechseln',
        step3: 'Änderungen vornehmen',
        step4: 'Rechtes Panel verwenden',
        step5: 'Regelmäßig speichern',
      },
      segmenting: 'Bild wird segmentiert...',
      waitingInQueue: 'Wartend in der Warteschlange',
      reloadingSegmentation: 'Aktualisierung der Segmentierungsdaten...',
    },
    exportFeatures: {
      title: 'Export-Funktionen',
      description: 'Umfassende Export-Möglichkeiten.',
      packageContents: 'Paket-Inhalte',
      contents: {
        originalImages: {
          title: 'Original-Bilder',
          description: 'Hochwertige mikroskopische Originalbilder.',
        },
        visualizations: {
          title: 'Visualisierungen',
          description: 'Annotierte Bilder mit nummerierten Polygonen.',
        },
      },
      annotationFormats: 'Annotations-Formate',
      formats: {
        coco: 'COCO-Format: Standard für PyTorch und TensorFlow',
        yolo: 'YOLO-Format: Optimiert für YOLO-Modelle',
        json: 'Benutzerdefiniertes JSON: Strukturiertes detailliertes Format',
      },
      calculatedMetrics: 'Berechnete Metriken',
      metricsDescription: 'SpheroSeg berechnet automatisch Metriken.',
      metricsCategories: {
        basic: {
          title: 'Basis-Messungen:',
          items: {
            area: 'Fläche',
            perimeter: 'Umfang',
            diameter: 'Äquivalenter Durchmesser',
            circularity: 'Zirkularität',
          },
        },
        advanced: {
          title: 'Erweiterte Metriken:',
          items: {
            feret: 'Feret-Durchmesser',
            majorMinor: 'Haupt-/Nebendurchmesser',
            compactness: 'Kompaktheit, Konvexität',
            sphericity: 'Sphärizitäts-Index',
          },
        },
      },
      exportFormats: 'Metriken-Export-Formate',
      exportFormatsList: {
        excel: 'Excel (.xlsx): Formatierte Tabelle',
        csv: 'CSV: Kommagetrennte Werte',
        jsonExport: 'JSON: Strukturiertes Format',
      },
      visualizationCustomization: 'Visualisierungs-Anpassung',
      customizationOptions: {
        colors: 'Anpassbare Polygon-Farben',
        numbering: 'Ein-/ausblendbare Nummerierung',
        strokeWidth: 'Anpassbare Strichstärke',
        fontSize: 'Kontrollierbare Schriftgröße',
        transparency: 'Einstellbare Transparenz',
      },
      howToExport: 'So Exportieren',
      exportSteps: {
        step1: 'Zum Dashboard navigieren',
        step2: 'Bilder auswählen',
        step3: 'Auf "Erweiterten Export" klicken',
        step4: 'Einstellungen konfigurieren',
        step5: 'Zusammenfassung überprüfen',
        step6: 'Auf "Export Starten" klicken',
      },
      exportNote: 'Pakete sind umfassend:',
      exportNoteText: 'Jeder Export enthält Dokumentation und Metadaten.',
    },
    sharedProjectsSection: {
      title: 'Geteilte Projekte',
      description:
        'SpheroSeg ermöglicht Ihnen die Zusammenarbeit mit Kollegen durch das Teilen von Projekten. Teilen Sie Ihre Segmentierungsergebnisse und Annotationen mit anderen Forschern zur Überprüfung und Zusammenarbeit.',
      sharingFeatures: 'Sharing-Funktionen',
      features: {
        readOnly:
          'Schreibgeschützter Zugriff: Empfänger können anzeigen, aber nicht ändern',
        emailInvite:
          'E-Mail-Einladungen: Teilen per E-Mail mit automatischen Benachrichtigungen',
        revokeAccess:
          'Widerrufbarer Zugriff: Sharing-Berechtigungen jederzeit entfernen',
        multipleCollaborators:
          'Mehrere Mitarbeiter: Mit ganzen Forschungsteams teilen',
      },
      howToShare: 'Wie man ein Projekt teilt',
      shareSteps: {
        step1: 'Öffnen Sie das Projekt, das Sie teilen möchten',
        step2:
          'Klicken Sie auf den "Teilen"-Button in der Projekt-Symbolleiste',
        step3: 'Geben Sie die E-Mail-Adressen Ihrer Mitarbeiter ein',
        step4:
          'Fügen Sie eine optionale Nachricht zur Erklärung des geteilten Inhalts hinzu',
        step5: 'Klicken Sie auf "Einladungen senden", um das Projekt zu teilen',
      },
      permissionsNote: 'Wichtig:',
      permissionsNoteText:
        'Geteilte Projekte sind für Empfänger schreibgeschützt. Sie können Segmentierungen anzeigen und Daten exportieren, aber die ursprünglichen Annotationen nicht ändern. Dies gewährleistet Datenintegrität bei gleichzeitiger Zusammenarbeit.',
    },
    footer: {
      backToHome: 'Zurück zur Startseite',
      backToTop: 'Nach Oben',
    },
  },
  legal: {
    terms: {
      title: 'Nutzungsbedingungen',
      lastUpdated: 'Zuletzt aktualisiert: Januar 2025',
      disclaimer:
        'Durch die Nutzung von SpheroSeg stimmen Sie diesen Bedingungen zu. Bitte lesen Sie sie sorgfältig.',
      sections: {
        acceptance: {
          title: '1. Annahme der Bedingungen',
          content:
            'Durch den Zugriff auf oder die Nutzung von SpheroSeg ("der Dienst") stimmen Sie zu, an diese Nutzungsbedingungen ("Bedingungen") und alle anwendbaren Gesetze und Vorschriften gebunden zu sein. Wenn Sie mit diesen Bedingungen nicht einverstanden sind, ist Ihnen die Nutzung dieses Dienstes untersagt. Diese Bedingungen stellen eine rechtlich bindende Vereinbarung zwischen Ihnen und SpheroSeg dar.',
        },
        useLicense: {
          title: '2. Nutzungslizenz und Erlaubte Nutzung',
          content:
            'Die Berechtigung zur Nutzung von SpheroSeg wird gewährt für:',
          permittedUses: [
            'Persönliche, nicht-kommerzielle Forschungszwecke',
            'Akademische und Bildungsforschung',
            'Wissenschaftliche Publikationen und Studien',
            'Biomedizinische Forschung und Analyse',
          ],
          licenseNote:
            'Dies ist die Gewährung einer Lizenz, nicht eine Eigentumsübertragung. Sie dürfen den Dienst nicht für kommerzielle Zwecke ohne ausdrückliche schriftliche Zustimmung nutzen.',
        },
        dataUsage: {
          title: '3. Datennutzung und Maschinelles Lernen',
          importantTitle: 'Wichtig: Verwendung Ihrer Daten',
          importantContent:
            'Durch das Hochladen von Bildern und Daten zu SpheroSeg stimmen Sie zu, dass wir diese Daten verwenden, um unsere maschinellen Lernmodelle für bessere Segmentierungsgenauigkeit zu verbessern und zu trainieren.',
          ownershipTitle: 'Dateneigentum:',
          ownershipContent:
            'Sie behalten das Eigentum an allen Daten, die Sie zu SpheroSeg hochladen. Durch die Nutzung unseres Dienstes gewähren Sie uns jedoch die Berechtigung zu:',
          permissions: [
            'Verarbeitung Ihrer Bilder für Segmentierungsanalyse',
            'Verwendung hochgeladener Daten (in anonymisierter Form) zur Verbesserung unserer ML-Algorithmen',
            'Verbesserung der Modellgenauigkeit durch kontinuierliches Lernen',
            'Entwicklung neuer Funktionen und Segmentierungsfähigkeiten',
          ],
          protectionNote:
            'Alle für ML-Training verwendeten Daten werden anonymisiert und von identifizierenden Informationen befreit. Wir teilen Ihre Rohdaten nicht ohne ausdrückliche Zustimmung mit Dritten.',
        },
        userResponsibilities: {
          title: '4. Benutzerpflichten',
          content: 'Sie verpflichten sich:',
          responsibilities: [
            'Den Dienst nur für rechtmäßige Zwecke zu nutzen',
            'Rechte des geistigen Eigentums zu respektieren',
            'Nicht zu versuchen, den Dienst rückzuentwickeln oder zu kompromittieren',
            'Bei der Kontoerstellung genaue Informationen anzugeben',
            'Die Sicherheit Ihrer Kontoanmeldedaten zu wahren',
          ],
        },
        serviceAvailability: {
          title: '5. Dienstverfügbarkeit und Einschränkungen',
          content:
            'Obwohl wir uns bemühen, kontinuierliche Dienstverfügbarkeit aufrechtzuerhalten, wird SpheroSeg "wie besehen" ohne Garantien jeglicher Art bereitgestellt. Wir garantieren keinen ununterbrochenen Zugang, und der Dienst kann Wartung, Updates oder vorübergehender Nichtverfügbarkeit unterliegen.',
        },
        limitationLiability: {
          title: '6. Haftungsbeschränkung',
          content:
            'In keinem Fall haften SpheroSeg, seine Entwickler oder verbundene Unternehmen für indirekte, zufällige, besondere, Folge- oder Strafschäden, einschließlich, aber nicht beschränkt auf Datenverlust, Gewinne oder Geschäftsmöglichkeiten, die aus Ihrer Nutzung des Dienstes entstehen.',
        },
        privacy: {
          title: '7. Datenschutz und Datenschutz',
          content:
            'Ihre Privatsphäre ist uns wichtig. Bitte lesen Sie unsere Datenschutzrichtlinie, die regelt, wie wir Ihre persönlichen Informationen und Forschungsdaten sammeln, verwenden und schützen.',
        },
        changes: {
          title: '8. Änderungen der Bedingungen',
          content:
            'Wir behalten uns das Recht vor, diese Bedingungen jederzeit zu ändern. Änderungen werden sofort nach Veröffentlichung wirksam. Ihre weitere Nutzung des Dienstes stellt die Annahme der geänderten Bedingungen dar.',
        },
        termination: {
          title: '9. Kündigung',
          content:
            'Jede Partei kann diese Vereinbarung jederzeit kündigen. Nach Kündigung erlischt Ihr Recht auf Zugang zum Dienst sofort, obwohl diese Bedingungen bezüglich der vorherigen Nutzung in Kraft bleiben.',
        },
        governingLaw: {
          title: '10. Anwendbares Recht',
          content:
            'Diese Bedingungen unterliegen und werden in Übereinstimmung mit geltendem Recht ausgelegt. Alle Streitigkeiten werden durch bindende Schiedsgerichtsbarkeit oder vor zuständigen Gerichten beigelegt.',
        },
      },
      contact: {
        title: 'Kontaktinformationen:',
        content:
          'Wenn Sie Fragen zu diesen Bedingungen haben, kontaktieren Sie uns <NAME_EMAIL>',
      },
      navigation: {
        backToHome: 'Zurück zur Startseite',
        privacyPolicy: 'Datenschutzrichtlinie',
      },
    },
    privacy: {
      title: 'Datenschutzrichtlinie',
      lastUpdated: 'Zuletzt aktualisiert: Januar 2025',
      disclaimer:
        'Ihre Privatsphäre ist uns wichtig. Diese Richtlinie erklärt, wie wir Ihre Daten sammeln, verwenden und schützen.',
      sections: {
        introduction: {
          title: '1. Einführung',
          content:
            'Diese Datenschutzrichtlinie erklärt, wie SpheroSeg ("wir", "uns", "unser") Ihre Informationen sammelt, verwendet, schützt und teilt, wenn Sie unsere Plattform für Sphäroid-Segmentierung und -Analyse nutzen. Durch die Nutzung unseres Dienstes stimmen Sie den in dieser Richtlinie beschriebenen Datenpraktiken zu.',
        },
        informationCollected: {
          title: '2. Informationen, die Wir Sammeln',
          content:
            'Wir sammeln Informationen, die Sie uns direkt bereitstellen, wenn Sie ein Konto erstellen, Bilder hochladen, Projekte erstellen und mit unseren Diensten interagieren.',
          personalInfo: {
            title: '2.1 Persönliche Informationen',
            items: [
              'Name und E-Mail-Adresse',
              'Institutionelle oder organisatorische Zugehörigkeit',
              'Kontoanmeldedaten und Präferenzen',
              'Kontaktinformationen für Support-Anfragen',
            ],
          },
          researchData: {
            title: '2.2 Forschungsdaten und Bilder',
            ownershipTitle: 'Ihre Forschungsdaten',
            ownershipContent:
              'Sie behalten das vollständige Eigentum an allen Bildern und Forschungsdaten, die Sie zu SpheroSeg hochladen. Wir beanspruchen niemals das Eigentum an Ihren Inhalten.',
            items: [
              'Bilder, die Sie zur Analyse hochladen',
              'Projekt-Metadaten und Einstellungen',
              'Segmentierungsergebnisse und Annotationen',
              'Analyseparameter und benutzerdefinierte Konfigurationen',
            ],
          },
          usageInfo: {
            title: '2.3 Nutzungsinformationen',
            items: [
              'Protokolldaten und Zugriffszeitstempel',
              'Geräteinformationen und Browser-Typ',
              'Nutzungsmuster und Feature-Interaktionen',
              'Leistungsmetriken und Fehlerberichte',
            ],
          },
        },
        mlTraining: {
          title: '3. Maschinelles Lernen und Datenverbesserung',
          importantTitle: 'Wichtig: Verwendung Ihrer Daten für KI-Training',
          importantIntro:
            'Um unsere Segmentierungsalgorithmen kontinuierlich zu verbessern, können wir hochgeladene Bilder und Daten verwenden, um unsere maschinellen Lernmodelle zu trainieren und zu verbessern.',
          controlTitle: 'Sie haben vollständige Kontrolle über Ihre Daten:',
          controlContent:
            'Bei der Kontoerstellung können Sie wählen, ob Sie die Verwendung Ihrer Daten für ML-Training zulassen. Sie können diese Präferenzen jederzeit ändern.',
          manageTitle: 'Um Ihre Zustimmung zu verwalten:',
          manageContent:
            'Gehen Sie zu Einstellungen → Datenschutz-Tab in Ihrem Dashboard. Dort können Sie die ML-Training-Zustimmung aktivieren oder deaktivieren und spezifische Zwecke (Algorithmusverbesserung, Feature-Entwicklung) wählen, für die Ihre Daten verwendet werden können.',
          howWeUse: {
            title: 'Wie Wir Ihre Daten für ML Verwenden:',
            items: [
              'Modelltraining: Bilder werden verwendet, um Segmentierungsalgorithmen für bessere Genauigkeit zu trainieren',
              'Algorithmusverbesserung: Ihre Segmentierungskorrekturen helfen, die automatische Erkennung zu verbessern',
              'Feature-Entwicklung: Nutzungsmuster leiten die Entwicklung neuer Analysewerkzeuge',
              'Qualitätssicherung: Daten helfen, neue Modellversionen zu validieren und zu testen',
            ],
          },
          protection: {
            title: 'Datenschutz im ML-Training:',
            items: [
              'Anonymisierung: Alle Daten werden vor der Verwendung im ML-Training anonymisiert',
              'Metadaten-Entfernung: Persönliche und institutionelle identifizierende Informationen werden entfernt',
              'Sichere Verarbeitung: Training erfolgt in sicheren, isolierten Umgebungen',
              'Keine Rohdatenverteilung: Ihre ursprünglichen Bilder werden niemals mit Dritten geteilt',
            ],
          },
        },
        howWeUse: {
          title: '4. Wie Wir Ihre Informationen Verwenden',
          content: 'Wir verwenden gesammelte Informationen für:',
          purposes: [
            'Bereitstellung und Wartung von Segmentierungsdiensten',
            'Verarbeitung Ihrer Bilder und Generierung von Analyseergebnissen',
            'Verbesserung unserer Algorithmen und Entwicklung neuer Funktionen',
            'Kommunikation mit Ihnen über Ihr Konto und Updates',
            'Bereitstellung technischer Unterstützung und Fehlerbehebung',
            'Erfüllung rechtlicher Verpflichtungen und Schutz unserer Rechte',
          ],
        },
        dataSecurity: {
          title: '5. Datensicherheit und -schutz',
          content:
            'Wir implementieren robuste Sicherheitsmaßnahmen einschließlich:',
          measures: [
            'Verschlüsselung von Daten in Transit und Ruhe',
            'Regelmäßige Sicherheitsaudits und Schwachstellenbewertungen',
            'Zugriffskontrollen und Authentifizierungssysteme',
            'Sichere Backup- und Disaster-Recovery-Verfahren',
            'Mitarbeitersicherheitsschulung und Zugriffsbeschränkungen',
          ],
        },
        dataSharing: {
          title: '6. Datenaustausch und Dritte',
          noSaleStatement:
            'Wir verkaufen Ihre persönlichen Informationen oder Forschungsdaten nicht.',
          sharingContent:
            'Wir können Informationen nur unter diesen begrenzten Umständen teilen:',
          circumstances: [
            'Mit Ihrer ausdrücklichen Zustimmung',
            'Zur Erfüllung rechtlicher Verpflichtungen oder Gerichtsbeschlüsse',
            'Mit vertrauenswürdigen Dienstleistern, die beim Betrieb unserer Plattform helfen (unter strengen Vertraulichkeitsvereinbarungen)',
            'Zum Schutz unserer Rechte, Sicherheit oder Eigentum',
            'In anonymisierter, aggregierter Form für Forschungsveröffentlichungen (mit Ihrer Zustimmung)',
          ],
        },
        privacyRights: {
          title: '7. Ihre Datenschutzrechte und Wahlmöglichkeiten',
          content: 'Sie haben das Recht auf:',
          rights: [
            'Zugang: Kopien Ihrer persönlichen Daten und Forschungsinhalte anfordern',
            'Berichtigung: Ungenaue Informationen aktualisieren oder korrigieren',
            'Löschung: Löschung Ihres Kontos und zugehöriger Daten anfordern',
            'Portabilität: Ihre Daten in einem maschinenlesbaren Format exportieren',
            'Opt-out: Ausschluss vom ML-Training anfordern. Hinweis: Dies kann folgende Funktionen einschränken: automatische Segmentierungsgenauigkeit, personalisierte Modellempfehlungen, adaptive Schwellenwertvorschläge, Batch-Verarbeitungsoptimierungen und zukünftige KI-gestützte Verbesserungen. Kontaktieren Sie den Support für spezifische Auswirkungen auf Ihr Konto.',
            'Einschränkung: Begrenzen, wie wir Ihre Informationen verarbeiten',
          ],
          contactNote:
            'Um diese Rechte auszuüben, kontaktieren Sie <NAME_EMAIL>. Wir werden innerhalb von 30 Tagen antworten.',
        },
        dataRetention: {
          title: '8. Datenspeicherung',
          content:
            'Wir unterscheiden zwischen persönlichen Daten und ML-Trainingsdaten:',
          categories: [
            'Persönliche/Kontodaten: Alle persönlichen Identifikatoren, Profilinformationen, Kontoeinstellungen und Transaktionshistorie werden innerhalb von 90 Tagen nach Kontoschluss dauerhaft gelöscht.',
            'Forschungsdaten: Ursprüngliche Bilder und Projektdaten, die mit Ihrem Konto verknüpft sind, werden innerhalb von 90 Tagen nach Kontoschluss gelöscht.',
            'ML-Trainingsdaten: Für ML-Training verwendete Daten werden zunächst anonymisiert/pseudonymisiert, um alle persönlichen Identifikatoren zu entfernen. Diese anonymisierten Daten können unbegrenzt aufbewahrt werden, um Modellverbesserungen zu bewahren, es sei denn, Sie schließen sich spezifisch vom ML-Training aus oder fordern vollständige Löschung an.',
            'Opt-out-Optionen: Sie können vollständige Löschung aller Daten, einschließlich anonymisierter ML-Trainingsdaten, durch <NAME_EMAIL> anfordern. Die Bearbeitungszeit beträgt typischerweise 30 Tage.',
          ],
        },
        internationalTransfers: {
          title: '9. Internationale Datenübertragungen',
          content:
            'Ihre Daten können in anderen Ländern als Ihrem eigenen verarbeitet werden. Wir sorgen für angemessene Schutzmaßnahmen und Schutz für internationale Übertragungen, einschließlich standardisierter Vertragsklauseln und Angemessenheitsentscheidungen.',
        },
        childrensPrivacy: {
          title: '10. Kinderdatenschutz',
          content:
            'Unser Dienst ist für Forscher bestimmt und richtet sich nicht an Kinder unter 16 Jahren. Wir sammeln wissentlich keine persönlichen Informationen von Kindern unter 16 Jahren. Wenn wir eine solche Sammlung entdecken, werden wir die Informationen umgehend löschen.',
        },
        policyChanges: {
          title: '11. Änderungen an Dieser Richtlinie',
          content:
            'Wir können diese Datenschutzrichtlinie aktualisieren, um Änderungen in unseren Praktiken oder rechtlichen Anforderungen widerzuspiegeln. Wir werden Sie über wesentliche Änderungen per E-Mail oder prominenten Hinweis auf unserer Website informieren. Fortgesetzte Nutzung stellt Annahme aktualisierter Bedingungen dar.',
        },
        contact: {
          title: '12. Kontaktinformationen',
          dpo: 'Datenschutzbeauftragter: <EMAIL>',
          general: 'Allgemeine Anfragen: <EMAIL>',
          postal: 'Postadresse:',
          address: {
            line1: 'ÚTIA AV ČR',
            line2: 'Pod Vodárenskou věží 4',
            line3: '182 08 Prag 8',
            line4: 'Tschechische Republik',
          },
        },
      },
      navigation: {
        backToHome: 'Zurück zur Startseite',
        termsOfService: 'Nutzungsbedingungen',
      },
    },
  },
  contextMenu: {
    editPolygon: 'Polygon bearbeiten',
    splitPolygon: 'Polygon teilen',
    deletePolygon: 'Polygon löschen',
    confirmDeletePolygon:
      'Sind Sie sicher, dass Sie dieses Polygon löschen möchten?',
    deletePolygonDescription:
      'Diese Aktion ist unumkehrbar. Das Polygon wird dauerhaft aus der Segmentierung entfernt.',
    duplicateVertex: 'Eckpunkt duplizieren',
    deleteVertex: 'Eckpunkt löschen',
  },
  websocket: {
    reconnecting: 'Verbinde erneut mit Server...',
    reconnected: 'Verbindung zum Server wiederhergestellt',
    reconnectFailed: 'Wiederherstellung der Serververbindung fehlgeschlagen',
    connectionLost: 'Verbindung zum Server verloren',
    connected: 'Mit Echtzeit-Updates verbunden',
    disconnected: 'Von Echtzeit-Updates getrennt',
  },
  metrics: {
    info: 'Metriken werden nur für externe Polygone ausgewertet. Flächen interner Polygone (Löcher) werden automatisch von den entsprechenden externen Polygonen abgezogen.',
    spheroid: 'Sphäroid',
    area: 'Fläche',
    perimeter: 'Umfang',
    equivalentDiameter: 'Äquivalenter Durchmesser',
    circularity: 'Zirkularität',
    feretMax: 'Feret Maximum',
    feretMin: 'Feret Minimum',
    compactness: 'Kompaktheit',
    convexity: 'Konvexität',
    solidity: 'Festigkeit',
    sphericity: 'Sphärizität',
    feretAspectRatio: 'Feret-Seitenverhältnis',
    noPolygonsFound: 'Keine Polygone zur Analyse gefunden',
  },
  keyboardShortcuts: {
    title: 'Tastaturkürzel',
    buttonLabel: 'Kürzel',
    viewMode: 'Ansichtsmodus',
    editVertices: 'Eckpunkt-Bearbeitungsmodus',
    addPoints: 'Punkte-Hinzufügen-Modus',
    createPolygon: 'Neues Polygon erstellen',
    sliceMode: 'Schnittmodus',
    deleteMode: 'Löschmodus',
    holdToAutoAdd: 'Halten für automatisches Hinzufügen von Punkten',
    undo: 'Rückgängig',
    redo: 'Wiederholen',
    deleteSelected: 'Ausgewähltes Polygon löschen',
    cancelOperation: 'Aktuelle Operation abbrechen',
    zoomIn: 'Hineinzoomen',
    zoomOut: 'Herauszoomen',
    resetView: 'Ansicht zurücksetzen',
    helperText:
      'Diese Kürzel funktionieren im Segmentierungseditor für schnellere und bequemere Arbeit.',
  },
  accessibility: {
    toggleSidebar: 'Seitenleiste umschalten',
    toggleMenu: 'Menü umschalten',
    selectLanguage: 'Sprache auswählen',
    selectTheme: 'Theme auswählen',
    breadcrumb: 'Brotkrümel-Navigation',
    pagination: 'Seitennummerierung',
    close: 'Schließen',
    more: 'Mehr',
    goToPreviousPage: 'Zur vorherigen Seite gehen',
    goToNextPage: 'Zur nächsten Seite gehen',
    previousPage: 'Vorherige',
    nextPage: 'Nächste',
    morePages: 'Weitere Seiten',
    previousSlide: 'Vorherige Folie',
    nextSlide: 'Nächste Folie',
    gridView: 'Rasteransicht',
    listView: 'Listenansicht',
  },
  sharing: {
    processingInvitation: 'Einladung wird verarbeitet...',
    share: 'Teilen',
    shared: 'Geteilt',
    shareProject: 'Projekt teilen',
    shareDescription:
      'Projekt "{{title}}" mit Kollegen und Mitarbeitern teilen',
    shareByEmail: 'Per E-Mail teilen',
    shareByLink: 'Per Link teilen',
    emailAddress: 'E-Mail-Adresse',
    enterEmailPlaceholder: 'E-Mail-Adresse eingeben',
    optionalMessage: 'Optionale Nachricht',
    messagePlaceholder:
      'Fügen Sie der Einladung eine persönliche Nachricht hinzu...',
    sendInvitation: 'Einladung senden',
    sending: 'Wird gesendet...',
    emailSent: 'E-Mail-Einladung gesendet!',
    emailRequired: 'E-Mail-Adresse ist erforderlich',
    emailShareFailed: 'Fehler beim Senden der E-Mail-Einladung',
    linkExpiry: 'Link-Ablauf',
    neverExpires: 'Läuft nie ab',
    hours: 'Stunden',
    days: 'Tage',
    generateLink: 'Link generieren',
    linkCopied: 'Link in die Zwischenablage kopiert!',
    sharedWithYou: 'Mit Ihnen geteilt',
    sharedBy: 'Geteilt von: {{email}}',
    sharedProjects: 'Geteilte Projekte',
    noSharedProjects: 'Es wurden keine Projekte mit Ihnen geteilt',
    removeFromShared: 'Aus Geteilten entfernen',
    acceptInvitation: 'Einladung annehmen',
    invitationAccepted:
      'Einladung angenommen! Das Projekt wurde zu Ihrem Dashboard hinzugefügt.',
    generating: 'Generiere...',
    linkGenerated: 'Freigabe-Link erstellt!',
    linkCopyFailed: 'Link konnte nicht kopiert werden',
    linkShareFailed: 'Freigabe-Link konnte nicht generiert werden',
    emailInvitations: 'E-Mail-Einladungen',
    shareLinks: 'Freigabe-Links',
    shareRevoked: 'Freigabe wurde widerrufen',
    acceptedUsers: 'Akzeptierte Benutzer',
    pendingInvitations: 'Ausstehende Einladungen',
    joinedViaLink: 'Über Link beigetreten',
    activeShareLinks: 'Aktive Freigabe-Links',
    joinedOn: 'Beigetreten am',
    sentOn: 'Gesendet am',
    joinedViaLinkOn: 'Beigetreten am',
    resendInvitation: 'Einladung erneut senden',
    invitationResent: 'Einladung erfolgreich erneut gesendet',
    resendFailed: 'Erneutes Senden der Einladung fehlgeschlagen',
    revokeAccess: 'Zugriff widerrufen',
    cancelInvitation: 'Einladung stornieren',
    reminderMessage:
      'Dies ist eine Erinnerung, dass Sie zur Zusammenarbeit an einem Projekt eingeladen wurden',
    revokeShareFailed: 'Widerrufen der Freigabe fehlgeschlagen',
    failedToLoadShares: 'Laden der Freigaben fehlgeschlagen',
    status: {
      pending: 'Ausstehend',
      accepted: 'Akzeptiert',
      revoked: 'Widerrufen',
    },
    invitationExpired: 'Diese Einladung ist abgelaufen',
    invitationInvalid: 'Ungültige Einladung',
    loginToAccept: 'Bitte melden Sie sich an, um diese Einladung anzunehmen',
    accepting: 'Akzeptiere',
    redirectingToProject: 'Weiterleitung zum Projekt',
    invitedEmail: 'Eingeladene E-Mail',
    loadingShare: 'Lade Freigabeinformationen...',
    projectSharedBy: 'Projekt geteilt von',
    signInRequired: 'Anmeldung erforderlich',
    signInToAccept: 'Bitte melden Sie sich an, um diese Einladung anzunehmen',
    signInButton: 'Anmelden',
    goToProject: 'Zum Projekt',
    backToHome: 'Zurück zur Startseite',
    acceptFailed: 'Annahme der Einladung fehlgeschlagen',
    differentEmail: 'Diese Einladung ist für eine andere E-Mail-Adresse',
  },
  error: 'Fehler',
  segmentationEditor: {
    reloadingSegmentation: 'Segmentierung wird neu geladen...',
    segmenting: 'Segmentierung läuft...',
    waitingInQueue: 'Warten in der Warteschlange...',
    error: {
      title: 'Segmentierungsfehler',
      description:
        'Beim Laden der Segmentierungsdaten ist ein Fehler aufgetreten. Dies könnte auf Netzwerkprobleme oder Serverprobleme zurückzuführen sein.',
      errorDetails: 'Fehlerdetails',
      tryAgain: 'Erneut versuchen',
      unsavedChanges: 'Ungespeicherte Änderungen',
    },
    export: {
      exportAllMetrics: 'Alle Metriken als XLSX exportieren',
      exportUnavailable: 'Export nicht verfügbar',
      loading: 'Laden...',
    },
  },
  footer: {
    appName: 'SpheroSeg',
    description:
      'Erweiterte Sphäroid-Segmentierungs- und Analyseplattform für biomedizinische Forscher mit KI-gestützten Tools für die mikroskopische Zellbildanalyse.',
    contact: 'Kontakt',
    institution: 'Institution',
    institutionName: 'ÚTIA AV ČR',
    address: 'Adresse',
    addressText: 'Pod Vodárenskou věží 4, 182 08 Prag 8',
    resources: 'Ressourcen',
    documentation: 'Dokumentation',
    features: 'Funktionen',
    tutorials: 'Tutorials',
    research: 'Forschung',
    legal: 'Rechtliches',
    termsOfService: 'Nutzungsbedingungen',
    privacyPolicy: 'Datenschutzrichtlinie',
    contactUs: 'Kontaktieren Sie uns',
    copyright:
      '© {{year}} SpheroSeg. Entwickelt am ÚTIA AV ČR (Institut für Informationstheorie und Automatisierung, Tschechische Akademie der Wissenschaften).',
  },
};
