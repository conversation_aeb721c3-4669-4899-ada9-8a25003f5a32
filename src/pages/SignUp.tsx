import React, { useState, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { useAuth, useLanguage } from '@/contexts/exports';
import { Loader2, ArrowLeft, Check, X } from 'lucide-react';
import { getErrorMessage } from '@/types';
import { getLocalizedErrorMessage } from '@/lib/errorUtils';
import { logger } from '@/lib/logger';

const SignUp = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { signUp, user } = useAuth();
  const { t } = useLanguage();

  // Real-time validation
  const passwordsMatch = useMemo(() => {
    if (!password || !confirmPassword) return null;
    return password === confirmPassword;
  }, [password, confirmPassword]);

  const showPasswordMatchIndicator = confirmPassword.length > 0;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      toast.error(t('errors.validationErrors.fieldRequired'));
      return;
    }

    if (password !== confirmPassword) {
      toast.error(t('errors.validationErrors.passwordsDoNotMatch'));
      return;
    }

    if (!agreeTerms) {
      toast.error(t('errors.validationErrors.confirmationRequired'));
      return;
    }

    setIsLoading(true);

    try {
      // Consent fields are collected via Terms of Service and Privacy Policy checkbox
      // Default consent values are set in backend during registration
      await signUp(email, password);
      // signUp already navigates to /dashboard automatically
    } catch (error) {
      logger.error('Sign up error:', error);
      const errorMessage = getLocalizedErrorMessage(
        error,
        t,
        'errors.operations.register'
      );
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // If already logged in, show a message instead of the form
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full glass-morphism rounded-2xl overflow-hidden shadow-glass-lg p-10 text-center">
          <h2 className="text-2xl font-bold mb-4">
            {t('auth.alreadyLoggedIn')}
          </h2>
          <p className="mb-6 text-gray-600">{t('auth.alreadySignedUp')}</p>
          <Button asChild className="w-full">
            <Link to="/dashboard">{t('auth.goToDashboard')}</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8 relative">
      {/* Back button - positioned at top left of screen */}
      <div className="absolute top-6 left-6 z-10">
        <Link
          to="/"
          className="inline-flex items-center justify-center w-10 h-10 glass-morphism rounded-full hover:bg-white/20 transition-all duration-200"
        >
          <ArrowLeft className="w-5 h-5 text-gray-700" />
        </Link>
      </div>

      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 rounded-full filter blur-3xl animate-float" />
        <div
          className="absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 rounded-full filter blur-3xl animate-float"
          style={{ animationDelay: '-2s' }}
        />
        <div
          className="absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 rounded-full filter blur-3xl animate-float"
          style={{ animationDelay: '-4s' }}
        />
      </div>

      <div className="flex items-center justify-center min-h-screen">
        <div className="max-w-md w-full glass-morphism rounded-2xl overflow-hidden shadow-glass-lg p-10 animate-scale-in">
          <div className="text-center mb-8">
            <Link to="/" className="inline-flex items-center justify-center">
              <img src="/logo.svg" alt="SpheroSeg Logo" className="w-12 h-12" />
            </Link>
            <h2 className="mt-4 text-3xl font-bold text-gray-900">
              {t('auth.createAccount')}
            </h2>
            <p className="mt-2 text-gray-600">{t('auth.signUpPlatform')}</p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Label htmlFor="email">{t('auth.emailAddress')}</Label>
              <Input
                id="email"
                type="email"
                autoComplete="username"
                placeholder={t('auth.emailPlaceholder')}
                value={email}
                onChange={e => setEmail(e.target.value)}
                className="h-11"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">{t('auth.password')}</Label>
              <Input
                id="password"
                type="password"
                autoComplete="new-password"
                placeholder={t('auth.passwordPlaceholder')}
                value={password}
                onChange={e => setPassword(e.target.value)}
                className="h-11"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">
                {t('auth.confirmPassword')}
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                autoComplete="new-password"
                placeholder={t('auth.passwordPlaceholder')}
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                className={`h-11 ${showPasswordMatchIndicator ? (passwordsMatch ? 'border-green-500' : 'border-red-500') : ''}`}
                required
              />
              {showPasswordMatchIndicator && (
                <div
                  className={`flex items-center gap-2 text-sm mt-1 ${passwordsMatch ? 'text-green-600' : 'text-red-600'}`}
                >
                  {passwordsMatch ? (
                    <>
                      <Check className="w-4 h-4" />
                      <span>{t('auth.passwordsMatch')}</span>
                    </>
                  ) : (
                    <>
                      <X className="w-4 h-4" />
                      <span>{t('auth.passwordsDoNotMatch')}</span>
                    </>
                  )}
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div className="flex items-center">
                <Checkbox
                  id="terms"
                  checked={agreeTerms}
                  onCheckedChange={checked => setAgreeTerms(checked as boolean)}
                />
                <label
                  htmlFor="terms"
                  className="ml-2 block text-sm text-gray-700"
                >
                  {t('auth.agreeToTermsCheckbox')}{' '}
                  <Link
                    to="/terms-of-service"
                    className="text-blue-600 hover:text-blue-500 transition-colors"
                  >
                    {t('auth.termsOfService')}
                  </Link>{' '}
                  {t('auth.and')}{' '}
                  <Link
                    to="/privacy-policy"
                    className="text-blue-600 hover:text-blue-500 transition-colors"
                  >
                    {t('auth.privacyPolicy')}
                  </Link>
                </label>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full h-11 text-base rounded-md"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('auth.creatingAccount')}
                </>
              ) : (
                t('auth.signUp')
              )}
            </Button>
          </form>

          <div className="mt-8 text-center">
            <p className="text-sm text-gray-600">
              {t('auth.alreadyHaveAccount')}{' '}
              <Link
                to="/sign-in"
                className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
              >
                {t('auth.signIn')}
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
