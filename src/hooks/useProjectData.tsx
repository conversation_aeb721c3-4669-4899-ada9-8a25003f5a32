import { logger } from '@/lib/logger';
import { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/contexts/useLanguage';
import apiClient, { SegmentationResultData } from '@/lib/api';
import type { SegmentationData } from '@/types';
import type { ProjectImage } from '@/types';
import { getErrorMessage } from '@/types';
import { getLocalizedErrorMessage } from '@/lib/errorUtils';

// Utility function to enrich images with segmentation results
// Now supports pagination to only fetch data for visible images
const enrichImagesWithSegmentation = async (
  images: ProjectImage[],
  options?: {
    startIndex?: number;
    endIndex?: number;
    fetchAll?: boolean;
  }
): Promise<ProjectImage[]> => {
  const { startIndex = 0, endIndex = images.length, fetchAll = false } = options || {};

  // Filter images that need segmentation data
  const imagesToEnrich = fetchAll 
    ? images.filter(img => {
        const status = img.segmentationStatus;
        return status === 'completed' || status === 'segmented';
      })
    : images
        .slice(startIndex, endIndex) // Only visible images
        .filter(img => {
          const status = img.segmentationStatus;
          return status === 'completed' || status === 'segmented';
        });

  logger.debug(
    `📊 Enriching images with segmentation data: ${images.length} total images, ${imagesToEnrich.length} to enrich (${fetchAll ? 'all' : `visible ${startIndex}-${endIndex}`})`
  );

  if (imagesToEnrich.length === 0) {
    logger.debug('ℹ️ No completed images found for segmentation enrichment');
    return images;
  }

  // Limit concurrent requests to prevent overwhelming the server during bulk operations
  const maxConcurrent = Math.min(imagesToEnrich.length, fetchAll ? 5 : 10);
  
  try {
    logger.debug(
      `🔄 Fetching segmentation data for ${imagesToEnrich.length} images (max ${maxConcurrent} concurrent)...`
    );

    // Process in batches to avoid overwhelming the server
    const batchSize = maxConcurrent;
    const segmentationResults = [];
    
    for (let i = 0; i < imagesToEnrich.length; i += batchSize) {
      const batch = imagesToEnrich.slice(i, i + batchSize);
      const batchPromises = batch.map(async (img, batchIndex) => {
        try {
          const absoluteIndex = i + batchIndex;
          logger.debug(
            `📥 Fetching segmentation for image ${absoluteIndex + 1}/${imagesToEnrich.length} (ID: ${img.id.slice(0, 8)}...)`
          );
          const segmentationData = await apiClient.getSegmentationResults(img.id);

        logger.debug(
          `✅ Successfully fetched segmentation for ${img.id.slice(0, 8)}: ${segmentationData?.polygons?.length || 0} polygons, ${segmentationData?.imageWidth || 'unknown'}x${segmentationData?.imageHeight || 'unknown'}`,
          {
            segmentationData,
          }
        );

        return {
          imageId: img.id,
          result: segmentationData
            ? {
                polygons: segmentationData.polygons || [],
                imageWidth: segmentationData.imageWidth || img.width || null,
                imageHeight: segmentationData.imageHeight || img.height || null,
                modelUsed: segmentationData.modelUsed,
                confidence: segmentationData.confidence,
                processingTime: segmentationData.processingTime,
                levelOfDetail: 'medium', // Default level of detail for thumbnails
                polygonCount: segmentationData.polygons?.length || 0,
                pointCount:
                  segmentationData.polygons?.reduce(
                    (sum, p) => sum + p.points.length,
                    0
                  ) || 0,
                compressionRatio: 1.0, // Default compression ratio
              }
            : null,
        };
      } catch (error) {
        // Check if it's a 404 error (no segmentation found)
        if (
          error &&
          typeof error === 'object' &&
          'response' in error &&
          (error as { response?: { status?: number } }).response?.status === 404
        ) {
          logger.debug(
            `ℹ️ No segmentation found for image ${img.id.slice(0, 8)} (404) - this is normal for images pending segmentation`
          );
        } else {
          logger.error(
            `❌ Failed to fetch segmentation results for image ${img.id.slice(0, 8)}:`,
            error
          );
        }
        return null;
      }
    });
      
      const batchResults = await Promise.all(batchPromises);
      segmentationResults.push(...batchResults);
      
      // Add small delay between batches to prevent overwhelming the server
      if (i + batchSize < imagesToEnrich.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Create a map of imageId to segmentation results
    const segmentationMap = new Map();
    let successfulEnrichments = 0;
    segmentationResults.forEach(result => {
      if (result) {
        segmentationMap.set(result.imageId, result.result);
        successfulEnrichments++;
      }
    });

    logger.debug(
      `📈 Successfully enriched ${successfulEnrichments} out of ${imagesToEnrich.length} images with segmentation data`
    );

    // Enrich images with segmentation results
    const enrichedImages = images.map(img => {
      const segmentationResult = segmentationMap.get(img.id);
      if (segmentationResult) {
        logger.debug(
          `🎯 Image ${img.id.slice(0, 8)} enriched with ${segmentationResult.polygons?.length || 0} polygons`
        );
      }
      return {
        ...img,
        segmentationResult: segmentationResult || img.segmentationResult,
      };
    });

    return enrichedImages;
  } catch (error) {
    logger.error('Error enriching images with segmentation results:', error);
    // Return original images if enrichment fails
    return images;
  }
};

export const useProjectData = (
  projectId: string | undefined,
  userId: string | undefined,
  options?: {
    fetchAll?: boolean;
    visibleRange?: { start: number; end: number };
  }
) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [projectTitle, setProjectTitle] = useState<string>('');
  const [images, setImages] = useState<ProjectImage[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Track pending requests to prevent duplicates
  const pendingRequestsRef = useRef<Set<string>>(new Set());

  // Store navigate function in ref to avoid dependency issues
  const navigateRef = useRef(navigate);
  navigateRef.current = navigate;

  // Store images without segmentation data
  const [imagesBase, setImagesBase] = useState<ProjectImage[]>([]);
  
  useEffect(() => {
    const fetchData = async () => {
      if (!projectId || !userId) {
        setLoading(false);
        return;
      }

      try {
        // First check if project exists
        const project = await apiClient.getProject(projectId);

        if (!project) {
          toast.error(t('errors.notFound'));
          navigateRef.current('/dashboard');
          return;
        }

        setProjectTitle(project.name);

        // Fetch all images by making multiple requests if needed
        // Backend has a max limit of 50 images per request
        let allImages: any[] = [];
        let page = 1;
        let hasMore = true;
        const limit = 50; // Maximum allowed by backend

        // Always fetch all images to ensure proper pagination on frontend
        while (hasMore) {
          try {
            const imagesResponse = await apiClient.getProjectImages(projectId, {
              limit,
              page,
            });

            if (
              !imagesResponse.images ||
              !Array.isArray(imagesResponse.images)
            ) {
              logger.error('Invalid images response format');
              break;
            }

            allImages = [...allImages, ...imagesResponse.images];

            // Check if we've fetched all images
            hasMore = page * limit < imagesResponse.total;
            page++;

            // Safety limit to prevent infinite loops (max 2000 images)
            if (page > 40) {
              logger.warn('Reached maximum pagination limit (2000 images)');
              break;
            }
          } catch (error) {
            logger.error(`Error fetching images page ${page}`, error);
            break;
          }
        }

        const imagesData = allImages;

        const formattedImages: ProjectImage[] = (imagesData || []).map(img => {
          // Normalize segmentation status from different backend field names
          let segmentationStatus =
            img.segmentationStatus || img.segmentation_status;

          // Normalize status values to consistent format
          if (segmentationStatus === 'segmented') {
            segmentationStatus = 'completed';
          }

          return {
            id: img.id,
            name: img.name,
            url: img.url || img.image_url, // Use url field that's already mapped in api.ts
            width: img.width,
            height: img.height,
            thumbnail_url: img.thumbnail_url,
            createdAt: new Date(img.created_at || img.createdAt),
            updatedAt: new Date(img.updated_at || img.updatedAt),
            segmentationStatus: segmentationStatus,
            // Will be populated by enriching with segmentation results
            segmentationResult: undefined,
          };
        });

        // Store base images
        setImagesBase(formattedImages);
        
        // Initial enrichment for visible range only
        const enrichedImages = await enrichImagesWithSegmentation(
          formattedImages,
          {
            fetchAll: options?.fetchAll || false,
            startIndex: options?.visibleRange?.start,
            endIndex: options?.visibleRange?.end,
          }
        );

        setImages(enrichedImages);
      } catch (error: unknown) {
        logger.error('Error fetching project:', error);

        if (
          error &&
          typeof error === 'object' &&
          'response' in error &&
          (error as { response?: { status?: number } }).response?.status === 404
        ) {
          toast.error(t('errors.notFound'));
          navigateRef.current('/dashboard');
        } else {
          const errorMessage = getLocalizedErrorMessage(
            error,
            t,
            'errors.operations.loadProject'
          );
          toast.error(errorMessage);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, userId, t]);
  
  // Update segmentation data when visible range changes
  useEffect(() => {
    if (!imagesBase.length || loading) return;
    if (!options?.visibleRange) return;
    
    const enrichVisibleImages = async () => {
      logger.debug('Enriching visible images', {
        start: options.visibleRange?.start,
        end: options.visibleRange?.end,
      });
      
      const enrichedImages = await enrichImagesWithSegmentation(
        imagesBase,
        {
          fetchAll: false,
          startIndex: options.visibleRange.start,
          endIndex: options.visibleRange.end,
        }
      );
      
      setImages(enrichedImages);
    };
    
    enrichVisibleImages();
  }, [options?.visibleRange?.start, options?.visibleRange?.end, imagesBase, loading]);

  const updateImages = (
    newImages: ProjectImage[] | ((prev: ProjectImage[]) => ProjectImage[])
  ): void => {
    setImages(newImages);
    // Also update base images to maintain consistency
    setImagesBase(prevBase => {
      const updatedImages = typeof newImages === 'function' ? newImages(prevBase) : newImages;
      return updatedImages;
    });
  };

  // Function to refresh segmentation data for a specific image with deduplication
  const refreshImageSegmentation = async (imageId: string) => {
    // Check if request is already in progress
    if (pendingRequestsRef.current.has(imageId)) {
      logger.debug(
        `⏭️ Skipping duplicate request for image ${imageId.slice(0, 8)} - already in progress`
      );
      return;
    }

    try {
      // Mark request as pending
      pendingRequestsRef.current.add(imageId);
      logger.debug(
        `🔄 Refreshing segmentation data for image ${imageId.slice(0, 8)}...`
      );

      const segmentationData = await apiClient.getSegmentationResults(imageId);

      logger.debug(
        `✅ Successfully refreshed segmentation for ${imageId.slice(0, 8)}: ${segmentationData.polygons?.length || 0} polygons, ${segmentationData.imageWidth}x${segmentationData.imageHeight}`
      );

      setImages(prevImages =>
        prevImages.map(img => {
          if (img.id === imageId) {
            return {
              ...img,
              segmentationResult: {
                polygons: segmentationData.polygons || [],
                imageWidth: segmentationData.imageWidth || img.width || null,
                imageHeight: segmentationData.imageHeight || img.height || null,
                modelUsed: segmentationData.modelUsed,
                confidence: segmentationData.confidence,
                processingTime: segmentationData.processingTime,
              },
            };
          }
          return img;
        })
      );
    } catch (error) {
      logger.error(
        `❌ Failed to refresh segmentation data for image ${imageId.slice(0, 8)}:`,
        error
      );
    } finally {
      // Remove from pending requests
      pendingRequestsRef.current.delete(imageId);
    }
  };

  return {
    projectTitle,
    images,
    loading,
    updateImages,
    refreshImageSegmentation,
  };
};
