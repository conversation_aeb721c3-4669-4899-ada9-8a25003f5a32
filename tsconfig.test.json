{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.app.json",
  "compilerOptions": {
    // Test-specific overrides
    "jsx": "react-jsx",
    "allowImportingTsExtensions": false,
    "noEmit": true,
    "composite": true,

    // Test environment types
    "types": [
      "vitest/globals",
      "@testing-library/jest-dom",
      "node",
      "vite/client"
    ],
    "lib": ["ES2020", "DOM", "DOM.Iterable"],

    // Relaxed settings for tests (can use any/unsafe patterns)
    "noImplicitAny": false,
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,

    // Keep core safety features for tests
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": [
    "src/**/*.test.ts",
    "src/**/*.test.tsx",
    "src/**/*.spec.ts",
    "src/**/*.spec.tsx",
    "tests/**/*",
    "vitest.setup.ts",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ],
  "exclude": ["node_modules", "dist", "backend", "coverage"]
}
